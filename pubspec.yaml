name: likewallet
description: "A new SDK likewallet project."

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

#version: 2.3.1+2915 # ANDROID VERSION CODE
version: 2.3.1+13 # IOS VERSION CODE

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.6
  http: ^0.13.5
  get: ^4.6.5
  get_storage: ^2.1.1
  firebase_messaging: ^15.0.3
  firebase_core: ^3.2.0
  firebase_analytics: ^11.2.0
  firebase_auth: ^5.1.2
  firebase_storage: ^12.1.1
  flutter_svg: ^1.1.6
  cached_network_image: ^3.2.3
  ags_authrest2: ^1.0.1
  qr_flutter: ^4.0.0
  flutter_screenutil: ^5.9.3
  fluttertoast: ^8.2.4
  intl: ^0.18.0
  mobile_scanner: ^6.0.0
  package_info_plus: ^8.0.0
  permission_handler: ^11.3.0
  flutter_easyloading: ^3.0.3
  flutter_markdown: ^0.6.10
  global_configuration: ^2.0.0-nullsafety.1
  cloud_firestore: ^5.1.0
  loading_indicator: ^3.1.1
  lock_to_win:
    path: ./lock_to_win
  web3dart: ^2.2.0
  web_socket_channel: ^2.1.0
  flutter_contacts: ^1.1.9+2
  delayed_display: ^2.0.0
  modal_progress_hud_nsn: ^0.5.1
  camera: ^0.10.3+2
  url_launcher: ^6.3.1
  encrypt: ^5.0.1
  clippy_flutter: ^2.0.0-nullsafety.1
  qr_code_scanner: ^1.0.1
  scan: ^1.6.0
  image_picker: ^1.1.2
  image_gallery_saver: ^2.0.3
  share_plus: ^7.2.2
  path_provider: ^2.1.2
  carousel_slider: ^5.0.0
  dio: ^5.4.0
  webview_flutter: ^2.0.9
  flutter_launcher_icons: ^0.14.3
  flutter_local_notifications: ^17.2.4
  local_auth: ^2.3.0
  percent_indicator: ^4.2.3
  restart_app: ^1.2.1
  shorebird_code_push: ^1.1.6
  device_info_plus: ^10.0.1
  qr_code_tools: ^0.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0

flutter_launcher_icons:
  ios: true
  image_path: "assets/icon/icon.png"
  remove_alpha_ios: true

dependency_overrides:
  intl: ^0.19.0

flutter:
  uses-material-design: true

  assets:
    - assets/bip39.txt
    - assets/config.json
    - assets/cfg/
    - assets/cfg/assets/
    - assets/cfg/assets/config.json
    - assets/image/
    - assets/icon/
    - assets/image/locklike/
    - assets/image/like2crypto/
    - assets/svgs/
    - assets/animation/
    - assets/animation/kyc/
    - assets/image/ads/
    - assets/image/refer/
    - assets/image/history/
    - assets/image/choice_user/
    - assets/image/index/
    - assets/image/contact_us/
    - assets/image/login/
    - assets/image/banking/buylike/
    - assets/image/banking/cash/
    - assets/image/receive/
    - assets/image/banking/cash/
    - assets/image/take_photo/
    - assets/image/closed_system/
    - assets/image/banking/
    - assets/image/notification/
    - assets/image/banking/send/
    - assets/image/spendlike/
    - assets/image/banking/send/
    - assets/image/store/
    - assets/image/change_phone/
    - assets/image/feedback/
    - assets/image/pin_code/
    - assets/image/home/
    - assets/image/info/
    - assets/image/network/
    - assets/image/reward/
    - assets/image/alert_update/
    - assets/image/lendex/
    - assets/image/kyc/
    - assets/animatedIcons/
    - assets/
    - assets/image/LDX/homeLDX/
    - assets/image/LDX/ads/
    - assets/ldx/
    - assets/markdown/
    - shorebird.yaml

  fonts:
    - family: Proxima Nova
      fonts:
        - asset: assets/fonts/ProximaNova/ProximaNova-Black.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Bold.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Extrabld.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Light.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Regular.ttf
        - asset: assets/fonts/ProximaNova/ProximaNova-Semibold.ttf
    - family: IconHome
      fonts:
        - asset: assets/fonts/IconHome.ttf