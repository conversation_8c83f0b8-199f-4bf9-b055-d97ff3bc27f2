import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:likewallet/view/alert/alertPatchUpdate.dart';

class ShorebirdTestPage extends StatefulWidget {
  const ShorebirdTestPage({super.key});

  @override
  State<ShorebirdTestPage> createState() => _ShorebirdTestPageState();
}

class _ShorebirdTestPageState extends State<ShorebirdTestPage> {
  final shorebirdCodePush = ShorebirdCodePush();
  
  String _status = 'Ready to test';
  bool _isLoading = false;
  Map<String, dynamic> _shorebirdInfo = {};

  @override
  void initState() {
    super.initState();
    _loadShorebirdInfo();
  }

  Future<void> _loadShorebirdInfo() async {
    setState(() => _isLoading = true);
    
    try {
      final info = {
        // 'App ID': await shorebirdCodePush.appId(),
        // 'Release Version': await shorebirdCodePush.releaseVersion(),
        'Current Patch Number': await shorebirdCodePush.currentPatchNumber(),
        'Is Update Available': await shorebirdCodePush.isNewPatchAvailableForDownload(),
      };
      
      setState(() {
        _shorebirdInfo = info;
        _status = 'Shorebird info loaded';
      });
    } catch (e) {
      setState(() {
        _status = 'Error loading info: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _checkForUpdates() async {
    setState(() {
      _isLoading = true;
      _status = 'Checking for updates...';
    });

    try {
      print("🔍 Starting update check...");
      
      final isUpdateAvailable = await shorebirdCodePush.isNewPatchAvailableForDownload();
      
      print("📱 Update check result: $isUpdateAvailable");
      print("🏷️ Current patch number: ${await shorebirdCodePush.currentPatchNumber()}");
      // print("🆔 App ID: ${await shorebirdCodePush.appId()}");
      // print("🔢 Release version: ${await shorebirdCodePush.releaseVersion()}");
      
      setState(() {
        _status = isUpdateAvailable 
          ? 'Update available! 🎉' 
          : 'No updates available 😔';
      });
      
      if (isUpdateAvailable) {
        print("✅ Update available! Showing dialog...");
        _showUpdateDialog();
      }
    } catch (e, stackTrace) {
      print("❌ Error checking for updates: $e");
      print("📍 Stack trace: $stackTrace");
      setState(() {
        _status = 'Error: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showUpdateDialog() {
    showDialog(
      context: context,
      useSafeArea: false,
      builder: (_) => AlertUpdatePatchPage(),
    );
  }

  Future<void> _downloadAndInstallPatch() async {
    setState(() {
      _isLoading = true;
      _status = 'Downloading patch...';
    });

    try {
      await shorebirdCodePush.downloadUpdateIfAvailable();
      setState(() {
        _status = 'Patch downloaded! Restart app to apply.';
      });
    } catch (e) {
      setState(() {
        _status = 'Download failed: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff141322),
      appBar: AppBar(
        backgroundColor: const Color(0xff141322),
        title: const Text(
          'Shorebird Test',
          style: TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              color: Colors.white.withOpacity(0.1),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      _status,
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14.sp,
                      ),
                    ),
                    if (_isLoading) ...[
                      SizedBox(height: 8.h),
                      const LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Shorebird Info Card
            Card(
              color: Colors.white.withOpacity(0.1),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Shorebird Information',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    ..._shorebirdInfo.entries.map((entry) => Padding(
                      padding: EdgeInsets.symmetric(vertical: 2.h),
                      child: Row(
                        children: [
                          Text(
                            '${entry.key}: ',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              '${entry.value}',
                              style: TextStyle(
                                color: Colors.white60,
                                fontSize: 12.sp,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )).toList(),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Test Buttons
            ElevatedButton(
              onPressed: _isLoading ? null : _loadShorebirdInfo,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
              child: Text(
                'Refresh Shorebird Info',
                style: TextStyle(fontSize: 16.sp, color: Colors.white),
              ),
            ),
            
            SizedBox(height: 12.h),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _checkForUpdates,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
              child: Text(
                'Check for Updates',
                style: TextStyle(fontSize: 16.sp, color: Colors.white),
              ),
            ),
            
            SizedBox(height: 12.h),
            
            ElevatedButton(
              onPressed: _showUpdateDialog,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
              child: Text(
                'Force Show Update Dialog',
                style: TextStyle(fontSize: 16.sp, color: Colors.white),
              ),
            ),
            
            SizedBox(height: 12.h),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _downloadAndInstallPatch,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
              child: Text(
                'Download & Install Patch',
                style: TextStyle(fontSize: 16.sp, color: Colors.white),
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Instructions
            Card(
              color: Colors.yellow.withOpacity(0.1),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Testing Instructions:',
                      style: TextStyle(
                        color: Colors.yellow,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      '1. Create a release: shorebird release android\n'
                      '2. Install the release on device\n'
                      '3. Make code changes\n'
                      '4. Create a patch: shorebird patch android\n'
                      '5. Test the update check function',
                      style: TextStyle(
                        color: Colors.yellow.withOpacity(0.8),
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
