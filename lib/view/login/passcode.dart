import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/otherController/drawerController.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/biometric/biometricController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/navigationBar/mainNavigator.dart';
import 'package:local_auth/local_auth.dart';

enum _SupportState {
  unknown,
  supported,
  unsupported,
}

class PasscodePage extends StatefulWidget {
  const PasscodePage({super.key});

  @override
  State<PasscodePage> createState() => _PasscodePageState();
}

class _PasscodePageState extends State<PasscodePage> {

  int checkRound = 0;
  int passCodeColo = 0;
  List<String> passCode = [];
  List<String> passCodeAgain = [];
  bool showPINagain = false;
  final passStore = Storage.get(StorageKeys.passcode);
  bool allowPutNumber = true;
  late BiometricController biometricController;
  final LocalAuthentication auth = LocalAuthentication();

  Future<void> _checkBiometrics() async {
    late bool canCheckBiometrics;
    try {
      canCheckBiometrics = await auth.canCheckBiometrics;
    } catch (e) {
      canCheckBiometrics = false;
      print(e);
    }
    if (!mounted) {
      return;
    }

    setState(() {
      // _canCheckBiometrics = canCheckBiometrics;
    });
  }

  Future<void> _getAvailableBiometrics() async {
    late List<BiometricType> availableBiometrics;
    try {
      availableBiometrics = await auth.getAvailableBiometrics();
    } catch (e) {
      availableBiometrics = <BiometricType>[];
      print(e);
    }
    if (!mounted) {
      return;
    }

    setState(() {
      // _availableBiometrics = availableBiometrics;
    });
  }

  DrawerOwnController drawerCtrl = Get.isRegistered<DrawerOwnController>() ? Get.find<DrawerOwnController>() : Get.put(DrawerOwnController());

  void initState() {
    super.initState();
    biometricController = Get.put(BiometricController());

    _initBiometric(); // แยก async logic ไปในฟังก์ชันนี้

  }

  Future<void> _initBiometric() async {
    if(!drawerCtrl.activeTouchID.value) {
      // ถ้า Touch ID ไม่ถูกเปิดใช้งาน
      return;
    }
    await _checkBiometrics();
    await _getAvailableBiometrics();
    if (passStore != null && passStore.isNotEmpty && biometricController.shouldShowBiometric()) {
      _showBiometricAuthentication();
    }
  }


  /// Show biometric authentication when entering PIN
  Future<void> _showBiometricAuthentication() async {
    try {
      final result = await biometricController.authenticate(
        reason: 'authenticate_biometric'.tr,
      );

      if (result) {
        // Biometric authentication successful, navigate to main screen
        Get.off(
              () => const MainScreen(),
          transition: Transition.rightToLeft,
          duration: const Duration(milliseconds: 500),
        );
      }
    } catch (e) {
      print('Biometric authentication error: $e');
      // Continue with PIN entry if biometric fails
    }
  }

  Future<void> setCode(String pass) async {
    debugPrint(pass);

    if (!allowPutNumber) return;

    if (pass == 'remove') {
      if (passCode.isNotEmpty) {
        setState(() {
          passCode.removeLast();
          passCodeColo = passCode.length;
        });
      }
    } else if (passCode.length < 6) {
      setState(() {
        passCode.add(pass);
        passCodeColo = passCode.length;
      });
    } else if (passCodeAgain.length < 6) {
      setState(() {
        passCodeAgain.add(pass);
        passCodeColo = passCodeAgain.length;
      });
    }

    if (passCodeColo == 6) {
      setState(() {
        allowPutNumber = false;
      });

      if (passStore != null && passStore.isNotEmpty) {
        /// เข้ามาตรงนี้ คือไม่ได้สร้าง pin
        debugPrint("store matches");

        print(passStore);
        print(passCode);
        if (passCode.join() == passStore) {
          Get.off(
                () => const MainScreen(),
            transition: Transition.rightToLeft,
            duration: const Duration(milliseconds: 500),
          );
        } else {
          Get.snackbar(
            'setpin_wrong'.tr,
            'choice_user_secret_wrong_yes'.tr,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
          );

          Future.delayed(const Duration(milliseconds: 500), () {
            setState(() {
              passCode.clear();
              passCodeAgain.clear();
              passCodeColo = 0;
              showPINagain = false;
              allowPutNumber = true;
            });
          });
        }
      } else if (passCodeAgain.length == 6) {
        if (passCode.join() == passCodeAgain.join()) {
          // PIN matches
          debugPrint("PIN matches");
          final profileCtrl = Get.find<ProfileController>();
          await profileCtrl.getCurrentUser();
          Storage.save(StorageKeys.passcode, passCode.join());
          Storage.save(StorageKeys.login, true);

          Get.to(
                () => const MainScreen(),
            transition: Transition.rightToLeft,
            duration: const Duration(milliseconds: 500),
          );
        } else {
          Get.snackbar(
            'setpin_wrong'.tr,
            'choice_user_secret_wrong_yes'.tr,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
          );

          Future.delayed(const Duration(milliseconds: 500), () {
            setState(() {
              passCode.clear();
              passCodeAgain.clear();
              passCodeColo = 0;
              showPINagain = false;
              allowPutNumber = true;
            });
          });
        }
      } else {
        // กรณียังกรอกไม่ครบ 6 ตัวของ passCodeAgain
        Future.delayed(const Duration(milliseconds: 500), () {
          setState(() {
            passCodeColo = 0;
            showPINagain = true;
            allowPutNumber = true;
          });
        });
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: LikeWalletAppTheme.bule2_4,
      body: Container(
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-1.0, -0.5),
            end: Alignment(1.0, 1.0),
            colors: [LikeWalletAppTheme.bule2_7, LikeWalletAppTheme.bule2_7],
            stops: [0.0, 1.0],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.075,
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.75,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width * 0.75,
                    alignment: Alignment.centerLeft,
                    child: backButton(context, Colors.grey),
                  ),
                  const SizedBox(height: 20),
                  GestureDetector(
                    onTap: () {
                      if (passStore != null && passStore.isNotEmpty) {
                        debugPrint("forget pin");
                      }
                    },
                    child: Container(
                      child: Text(
                        passStore != null && passStore.isNotEmpty ? "forget_secret".tr :showPINagain ? "" :'setpin_explain'.tr,
                        style: TextStyle(
                          color: LikeWalletAppTheme.gray7.withOpacity(0.3),
                          fontFamily: 'Proxima Nova',
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.075,),
                ],
              ),
            ),
            Column(
              children: [
                Text(
                  passStore != null && passStore.isNotEmpty ? 'enter_pin'.tr :showPINagain ? 'setpin_again'.tr :'setpin'.tr,
                  style: TextStyle(
                    color: const Color(0xffFFFFFF),
                    fontFamily: 'Proxima Nova',
                    fontSize: 24.sp,
                  ),
                ),
                SizedBox(height: 18.h,),
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.45,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: List.generate(6, (index) {
                      return Container(
                        width: 12.w,
                        height: 12.w,
                        decoration: BoxDecoration(
                          color: passCodeColo - 1 >= index ? Colors.white :LikeWalletAppTheme.gray7.withOpacity(0.3),
                          // borderRadius: BorderRadius.circular(50),
                          shape: BoxShape.circle,
                        ),
                      );
                    },
                    ),
                  ),
                ),
              ],
            ),
            Container(
              width: MediaQuery.of(context).size.width * 0.75,
              padding: EdgeInsets.symmetric(vertical: 20.h),
              child: GridView.count(
                crossAxisCount: 3,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.2,
                mainAxisSpacing: 10.h,
                crossAxisSpacing: 10.w,
                children: [
                  // ปุ่ม 1-9
                  for (int i = 1; i <= 9; i++)
                    _buildNumberButton(i.toString()),
                  Container(),
                  // ปุ่ม 0
                  _buildNumberButton('0'),
                  // ปุ่มลบ
                  _buildClearButton(),
                ],
              ),
            ),
            Container(
              child: passStore != null &&
                  passStore.isNotEmpty &&
                  biometricController.shouldShowBiometric() ? Column(
                children: [
                  SizedBox(height: 20.h),
                  _buildBiometricButton(),
                  SizedBox(height: 20.h),
                  Text(
                    "pin_code_finger_print".tr,
                    style: TextStyle(
                      color: LikeWalletAppTheme.gray7.withOpacity(0.3),
                      fontFamily: 'Proxima Nova',
                      fontSize: 16.sp,
                    ),
                  ),
                ],
              ) : Container(),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildNumberButton(String number) {
    return InkWell(
      onTap: () => setCode(number),
      borderRadius: BorderRadius.circular(20),
      child: Container(
        decoration: const BoxDecoration(
          color: LikeWalletAppTheme.bule2_6,
          shape: BoxShape.circle,
        ),
        alignment: Alignment.center,
        child: Text(
          number,
          style: TextStyle(
            fontSize: 30.sp,
            color: LikeWalletAppTheme.bule1,
            fontFamily: 'Nimbus Sans',
            fontWeight: FontWeight.w100,
          ),
        ),
      ),
    );
  }

  Widget _buildClearButton() {
    return InkWell(
      onTap: () => setCode('remove'),
      borderRadius: BorderRadius.circular(50),
      child: Container(
        alignment: Alignment.center,
        child: Image.asset(
          LikeWalletImage.icon_clear,
          color: LikeWalletAppTheme.bule1,
          height: 18.h,
        ),
      ),
    );
  }

  Widget _buildBiometricButton() {
    return Obx(() => InkWell(
      onTap: biometricController.isAuthenticating.value
          ? null
          : () => _showBiometricAuthentication(),
      borderRadius: BorderRadius.circular(50),
      child: Container(
        alignment: Alignment.center,
        child: biometricController.isAuthenticating.value
            ? SizedBox(
          width: 40.w,
          height: 40.w,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              LikeWalletAppTheme.bule1,
            ),
          ),
        )
            : Image.asset(
          LikeWalletImage.icon_fingerprint,
          color: LikeWalletAppTheme.bule1,
          height: 64.h,
        ),
      ),
    ));
  }
}
