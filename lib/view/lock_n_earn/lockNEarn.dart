import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/lockNEarn/lockNEarnController.dart';


import '../../service/components.dart';

class LockLikeView extends StatelessWidget {

  final LockNEarnController lockNearnCtrl = Get.isRegistered<LockNEarnController>() ?Get.find<LockNEarnController>() :Get.put(LockNEarnController());

  final showConfirmButtons = false.obs;


  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            LikeWalletAppTheme.bule2_9,
            LikeWalletAppTheme.bule2_10,
            LikeWalletAppTheme.bule2_11,
          ],
          stops: [0.0, 0.493, 1.0],
        ),
        image: DecorationImage(
          image: AssetImage(LikeWalletImage.icon_locklike_image),
          alignment: Alignment.topLeft,
          scale: 1.7,
        ),
      ),
      child: lockNearnCtrl.isLoading.value
          ? Center(child: CircularProgressIndicator())
          : Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 12, left: 24),
              child: backButton(context, LikeWalletAppTheme.gray),
            ),
          ),
          _buildBalanceSection(),
          SizedBox(height: 20),
          _buildMainLockArea(),
          if (lockNearnCtrl.success.value)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text('✅ Success!', style: TextStyle(color: Colors.green, fontSize: 18)),
            ),
          SizedBox(height: 20),
          _buildConfirmButtons(),
          Spacer(),
          _buildCarousel(),
        ],
      ),
    ));
  }

  Widget _buildBalanceSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          SizedBox(height: 20),
          Text('lock_likepoint_all'.tr, style: TextStyle(color: Color(0xff2be8d8))),
          Text(lockNearnCtrl.amountUnlock.value, style: TextStyle(color: Color(0xff2be8d8), fontSize: 14)),
          SizedBox(height: 20),
          Text('lock_likepoint_locklike'.tr, style: TextStyle(color: Color(0xff2be8d8))),
          Text(lockNearnCtrl.lockedBalance.value, style: TextStyle(color: Color(0xff2be8d8), fontSize: 28)),
          SizedBox(height: 20),
          Text('lock_likepoint_balance'.tr, style: TextStyle(color: Color(0xff2be8d8))),
          Text(lockNearnCtrl.getAvailableBalance(), style: TextStyle(color: Color(0xff2be8d8), fontSize: 14)),
        ],
      ),
    );
  }

  Widget _buildMainLockArea() {
    return Container(
      height: 280.h,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 24),
              child: Image.asset(LikeWalletImage.locklike_lock_unlock, fit: BoxFit.fill),
            ),
          ),
          Positioned(
            top: 0,
            left: 48,
            right: 48,
            child: Row(
              children: [
                Expanded(child: _buildToggleButton(true, 'lock_lock_more'.tr)),
                SizedBox(width: 8),
                Expanded(child: _buildToggleButton(false, 'lock_unlock'.tr)),
              ],
            ),
          ),
          Positioned(
            top: 50,
            left: 48,
            right: 48,
            child: _buildInputArea(),
          ),
          Positioned(
            top: 200,
            right: 0,
            left: 0,
            child: _buildSetAllButton(),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton(bool isLock, String text) {
    return GestureDetector(
      onTap: () => lockNearnCtrl.toggleMode(isLock),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          image: lockNearnCtrl.selected.value == isLock ? DecorationImage(image: AssetImage(LikeWalletImage.locklike_button_select)) : null,
          borderRadius: BorderRadius.circular(50),
        ),
        child: Center(child: Text(text, style: TextStyle(color: Colors.black, fontSize: 16))),
      ),
    );
  }

  Widget _buildInputArea() {
    final isLock = lockNearnCtrl.selected.value;
    final amountController = isLock ? lockNearnCtrl.amountLock : lockNearnCtrl.amountUnLock;

    return Obx(() {
      final hasPreview = lockNearnCtrl.unlockPreviewText.value.isNotEmpty;

      return Column(
        children: [
          SizedBox(
            height: 100,
            child: Center(
              child: AnimatedSwitcher(
                duration: Duration(milliseconds: 300),
                transitionBuilder: (child, animation) {
                  final slide = Tween<Offset>(
                    begin: Offset(1.0, 0.0),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

                  final fade = Tween<double>(begin: 0, end: 1).animate(animation);

                  return SlideTransition(
                    position: slide,
                    child: FadeTransition(opacity: fade, child: child),
                  );
                },
                child: hasPreview
                    ? Padding(
                  key: ValueKey('preview_text'),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    lockNearnCtrl.unlockPreviewText.value,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                )
                    : TextField(
                  key: ValueKey('amount_input'),
                  controller: amountController,
                  inputFormatters: [ThousandsFormatter()],
                  decoration: InputDecoration(
                    hintText: '0',
                    hintStyle: TextStyle(color: Colors.white30),
                    border: InputBorder.none,
                  ),
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white, fontSize: 36),
                  keyboardType: TextInputType.number,
                ),
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 18),
            width: 30.w,
            child: Image.asset(LikeWalletImage.locklike_icon_unlock),
          ),
        ],
      );
    });
  }

  Widget _buildSetAllButton() {
    return Obx(() => AnimatedSwitcher(
      duration: Duration(milliseconds: 300),
      transitionBuilder: (child, animation) {
        final offsetAnimation = Tween<Offset>(
          begin: Offset(1.0, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return SlideTransition(position: offsetAnimation, child: child);
      },
      child: lockNearnCtrl.showConfirmButtons.value
          ? SizedBox(key: ValueKey('hide_all'))
          : Padding(
        key: ValueKey('show_all'),
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            GestureDetector(
              onTap: lockNearnCtrl.setAllAmount,
              child: Container(
                width: 60.w,
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                decoration: BoxDecoration(
                  color: Color(0xff17171e),
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(color: Colors.black.withOpacity(0.3), blurRadius: 6, offset: Offset(0, 3))
                  ],
                ),
                child: Center(
                  child: Text(
                    'lock_button_all'.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Color(0xff00ffff), fontSize: 12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ));
  }


  Widget _buildConfirmButtons() {
    return Obx(() => AnimatedSwitcher(
      duration: Duration(milliseconds: 300),
      transitionBuilder: (child, animation) {
        final offsetAnimation = Tween<Offset>(
          begin: Offset(1.0, 0.0), // จากขวาเข้า
          end: Offset.zero,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return SlideTransition(position: offsetAnimation, child: child);
      },
      child: lockNearnCtrl.showConfirmButtons.value
          ? Padding(
        key: ValueKey('confirm_buttons'), // ต้องใช้ key เพื่อให้ switch ทำงาน
        padding: EdgeInsets.symmetric(horizontal: 12.0),
        child: Row(
          children: [
            Expanded(
              child: Align(
                alignment: Alignment.centerLeft,
                child: TextButton(
                  onPressed: lockNearnCtrl.cancelAction,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(LikeWalletImage.icon_button_cancel, width: 30, height: 30),
                      SizedBox(width: 6),
                      Text('ยกเลิก', style: TextStyle(color: Colors.cyan)),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              child: Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: lockNearnCtrl.handleActionButton,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('ยืนยัน', style: TextStyle(color: Colors.cyan)),
                      SizedBox(width: 6),
                      Image.asset(LikeWalletImage.icon_button_next, width: 30, height: 30),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      )
          : SizedBox(
        key: ValueKey('empty_space'),
      ),
    ));
  }


  Widget _buildCarousel() {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Image.asset(LikeWalletImage.locklike_BG_slider, fit: BoxFit.cover, width: double.infinity, height: 150),
        CarouselSlider(
          options: CarouselOptions(height: 150, autoPlay: true, enlargeCenterPage: true, viewportFraction: 1),
          items: lockNearnCtrl.carouselImages.map((imgPath) {
            return Builder(
              builder: (BuildContext context) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.asset(imgPath, width: double.infinity),
                );
              },
            );
          }).toList(),
        ),
      ],
    );
  }
}


class ThousandsFormatter extends TextInputFormatter {
  final NumberFormat _formatter = NumberFormat("#,###");

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // Remove all non-digit characters
    String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.isEmpty) return newValue.copyWith(text: '');

    final number = int.parse(digitsOnly);
    final newString = _formatter.format(number);

    return TextEditingValue(
      text: newString,
      selection: TextSelection.collapsed(offset: newString.length),
    );
  }
}