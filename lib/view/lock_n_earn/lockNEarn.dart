import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../controller/lockNEarn/lockNEarnController.dart';
import '../../service/components.dart';

class LockLikeView extends StatelessWidget {
  const LockLikeView({super.key});

  @override
  Widget build(BuildContext context) {

    final LockNEarnController lockNearnCtrl = Get.isRegistered<LockNEarnController>() ?Get.find<LockNEarnController>() :Get.put(LockNEarnController());

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            LikeWalletAppTheme.bule2_9,
            LikeWalletAppTheme.bule2_10,
            LikeWalletAppTheme.bule2_11,
          ],
          stops: [0.0, 0.493, 1.0],
        ),
        image: DecorationImage(
          image: AssetImage(LikeWalletImage.icon_locklike_image),
          alignment: Alignment.topLeft,
          scale: 1.7,
        ),
      ),
      child: Obx(() => lockNearnCtrl.isLoading.value
          ? Center(child: CircularProgressIndicator())
          : Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 12, left: 24),
              child: backButton(context, LikeWalletAppTheme.gray),
            ),
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                SizedBox(height: 20),
                Text('lock_likepoint_all'.tr, style: TextStyle(color: Color(0xff2be8d8))),
                Text(lockNearnCtrl.amountUnlock.value, style: TextStyle(color: Color(0xff2be8d8), fontSize: 14)),
                SizedBox(height: 20),
                Text('lock_likepoint_locklike'.tr, style: TextStyle(color: Color(0xff2be8d8))),
                Text(lockNearnCtrl.lockedBalance.value, style: TextStyle(color: Color(0xff2be8d8), fontSize: 28)),
                SizedBox(height: 20),
                Text('lock_likepoint_balance'.tr, style: TextStyle(color: Color(0xff2be8d8))),
                Text(lockNearnCtrl.getAvailableBalance(),
                    style: TextStyle(color: Color(0xff2be8d8), fontSize: 14)),
              ],
            ),
          ),
          SizedBox(height: 20),
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 280,
                margin: EdgeInsets.symmetric(horizontal: 24),
                alignment: Alignment.center,
                child: Image.asset(
                  LikeWalletImage.locklike_lock_unlock,
                  width: double.infinity,
                  height: 240,
                  fit: BoxFit.fill,
                ),
              ),
              Positioned(
                top: 24,
                left: 48,
                right: 48,
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => lockNearnCtrl.toggleMode(true),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 16),
                          decoration: BoxDecoration(
                            image: lockNearnCtrl.selected.value
                                ? DecorationImage(
                              image: AssetImage(LikeWalletImage.locklike_button_select),
                              fit: BoxFit.cover,
                            )
                                : null,
                            color: !lockNearnCtrl.selected.value ? Colors.transparent : null,
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Center(child: Text('lock_lock_more'.tr, style: TextStyle(color: Colors.black, fontSize: 16))),
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => lockNearnCtrl.toggleMode(false),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 16),
                          decoration: BoxDecoration(
                            image: !lockNearnCtrl.selected.value
                                ? DecorationImage(
                              image: AssetImage(LikeWalletImage.locklike_button_select),
                              fit: BoxFit.cover,
                            )
                                : null,
                            color: lockNearnCtrl.selected.value ? Colors.transparent : null,
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Center(child: Text('lock_unlock'.tr, style: TextStyle(color: Colors.black, fontSize: 16))),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Positioned(
                top: 120,
                left: 48,
                right: 48,
                child: lockNearnCtrl.selected.value
                    ? Column(
                  children: [
                    TextField(
                      controller: lockNearnCtrl.amountLock,
                      inputFormatters: [ThousandsFormatter()],
                      decoration: InputDecoration(
                        hintText: '0',
                        hintStyle: TextStyle(color: Colors.white30),
                        border: InputBorder.none,
                      ),
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.white, fontSize: 36),
                      keyboardType: TextInputType.number,
                    ),
                    Container(
                      padding: EdgeInsets.only(top: 24),
                      width: 30.w,
                      child: Image.asset(
                        LikeWalletImage.locklike_icon_unlock,
                      ),
                    ),
                  ],
                )
                    : Column(
                  children: [
                    TextField(
                      controller: lockNearnCtrl.amountUnLock,
                      inputFormatters: [
                        ThousandsFormatter()
                      ],
                      decoration: InputDecoration(
                        hintText: '0',
                        hintStyle: TextStyle(color: Colors.white30),
                        border: InputBorder.none,
                      ),
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.white, fontSize: 36),
                      keyboardType: TextInputType.number,
                    ),
                    Container(
                      padding: EdgeInsets.only(top: 24),
                      width: 30.w,
                      child: Image.asset(
                        LikeWalletImage.locklike_icon_unlock,
                      ),
                    ),
                  ],
                ),
              ),
// แก้ใน Positioned ของปุ่มทั้งหมด:
              Positioned(
                bottom: 10, // จากเดิม top: 240
                right: 26,   // ลดจาก 26 ให้มันชิดขอบมากขึ้น
                child: GestureDetector(
                  onTap: () => lockNearnCtrl.setAllAmount(),
                  child: Container(
                    width: 60.w,
                    padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color(0xff17171e),
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 6,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        "lock_button_all".tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(0xff00ffff),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 20),
          // Action Button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 48),
            child: GestureDetector(
              onTap: () => lockNearnCtrl.handleActionButton(),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xff00ffff), Color(0xff2be8d8)],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    lockNearnCtrl.selected.value ? 'lock_lock_more'.tr : 'lock_unlock'.tr,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
          if (lockNearnCtrl.success.value)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text('✅ Success!', style: TextStyle(color: Colors.green, fontSize: 18)),
            ),
          Spacer(),
          Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Image.asset(
                LikeWalletImage.locklike_BG_slider,
                fit: BoxFit.cover,
                width: double.infinity,
                height: 140,
              ),
              CarouselSlider(
                options: CarouselOptions(
                  height: 140,
                  autoPlay: true,
                  enlargeCenterPage: true,
                  viewportFraction: 1,
                ),
                items: lockNearnCtrl.carouselImages.map((imgPath) {
                  return Builder(
                    builder: (BuildContext context) {
                      return ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.asset(imgPath, fit: BoxFit.cover, width: double.infinity),
                      );
                    },
                  );
                }).toList(),
              ),
            ],
          ),
        ],
      )),
    );
  }
}

class ThousandsFormatter extends TextInputFormatter {
  final NumberFormat _formatter = NumberFormat("#,###");

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // Remove all non-digit characters
    String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.isEmpty) return newValue.copyWith(text: '');

    final number = int.parse(digitsOnly);
    final newString = _formatter.format(number);

    return TextEditingValue(
      text: newString,
      selection: TextSelection.collapsed(offset: newString.length),
    );
  }
}

