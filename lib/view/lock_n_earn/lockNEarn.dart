import 'dart:ui';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/lockNEarn/lockNEarnController.dart';
import 'package:likewallet/service/loading.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:intl/intl.dart' as formatIntl;


import '../../service/components.dart';

class LockLikeView extends StatefulWidget {

  @override
  State<LockLikeView> createState() => _LockLikeViewState();
}

class _LockLikeViewState extends State<LockLikeView> {
  final LockNEarnController lockNearnCtrl = Get.isRegistered<LockNEarnController>() ?Get.find<LockNEarnController>() :Get.put(LockNEarnController());

  final showConfirmButtons = false.obs;

  final f = new formatIntl.NumberFormat("###,###.##");

  @override
  Widget build(BuildContext context) {
    return Obx(() => ModalProgressHUD(
      opacity: 0.1,
      inAsyncCall: lockNearnCtrl.isLoading.value,
      progressIndicator: CustomLoading(),
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Scaffold(
          body: Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      LikeWalletAppTheme.bule2_9,
                      LikeWalletAppTheme.bule2_10,
                      LikeWalletAppTheme.bule2_11,
                    ],
                    stops: [0.0, 0.493, 1.0],
                  ),
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                child: SvgPicture.network (
                  LikeWalletImage.icon_locklike_image,
                  fit: BoxFit.cover,
                  width: 0.77.sw,
                  height: 0.34.sh,
                ),
              ),
              Column(
                children: [
                  Align(
                    alignment: Alignment.topLeft,
                    child: Padding(
                      padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 12, left: 24),
                      child: backButton(context, LikeWalletAppTheme.gray),
                    ),
                  ),
                  _buildBalanceSection(),
                  SizedBox(height: 20),
                  _buildMainLockArea(context),
                  if (lockNearnCtrl.success.value)
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text('✅ Success!', style: TextStyle(color: Colors.green, fontSize: 18)),
                    ),
                  // SizedBox(height: 20),
                  _buildConfirmButtons(),
                  Spacer(),
                  _buildCarousel(),
                ],
              ),
            ],
          ),
        ),
      ),
    ));
  }

  Widget _buildBalanceSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          SizedBox(height: 18.h),
          Text('lock_likepoint_all'.tr, style: TextStyle(color: Color(0xff2be8d8))),
          Text(f.format(double.parse(lockNearnCtrl.totalLike.value)), style: TextStyle(color: Color(0xff2be8d8), fontSize: 20.sp)),
          SizedBox(height: 18.h),
          Text('lock_likepoint_locklike'.tr, style: TextStyle(color: Color(0xff2be8d8))),
          Text(lockNearnCtrl.lockedBalance.value, style: TextStyle(color: Color(0xff2be8d8), fontSize: 36.sp)),
          SizedBox(height: 18.h),
          Text('lock_likepoint_balance'.tr, style: TextStyle(color: Color(0xff2be8d8))),
          Text(f.format(double.parse(lockNearnCtrl.amountUnlock.value)), style: TextStyle(color: Color(0xff2be8d8), fontSize: 20.sp)),
        ],
      ),
    );
  }

  Widget _buildMainLockArea(BuildContext context) {
    return SizedBox(
      height: 0.44.sh,
      width: double.infinity,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          // BG Image ติดขอบบนเสมอ
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              width: 350.w,
              height: 250.h,
              child: Image.asset(
                LikeWalletImage.locklike_lock_unlock,
                fit: BoxFit.contain,
              ),
            ),
          ),

          // Toggle Button ซ้อนบน BG (fix ระยะห่างจากบน)
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: EdgeInsets.only(bottom: 315.h , left: 28.w ,right: 28.w), // ปรับตรงนี้ได้
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(width: 20), // กันชิดซ้ายสุด
                  Expanded(child: _buildToggleButton(false, 'lock_lock_more'.tr)),
                  SizedBox(width: 8),
                  Expanded(child: _buildToggleButton(true, 'lock_unlock'.tr)),
                  SizedBox(width: 20),
                ],
              ),
            ),
          ),

          // AnimatedSwitcher (input/confirm)
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: EdgeInsets.only(top: 65), // สูงเท่า BG + ระยะห่าง
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.7, // จำกัดความกว้าง
                child: AnimatedSwitcher(
                  duration: Duration(milliseconds: 300),
                  child: lockNearnCtrl.onTapUnlock.value
                      ? _buildInputArea(context)
                      : _buildConfirmUnlock(),
                ),
              ),
            ),
          ),

          // Set All Button ล่างสุด
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: EdgeInsets.only(top: 230.h),
              child: InkWell(
                onTap: () {
                  lockNearnCtrl.changeOnTapUnlock();
                },
                child: _buildSetAllButton(context),
              ),
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildToggleButton(bool isLock, String text) {
    return GestureDetector(
      onTap: () {
        // lockNearnCtrl.toggleMode(isLock);
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          image: lockNearnCtrl.selected.value == isLock ? DecorationImage(image: AssetImage(LikeWalletImage.locklike_button_select)) : null,
          borderRadius: BorderRadius.circular(50),
        ),
        child: Center(child: Text(text, style: TextStyle(color: Colors.black, fontSize: 16))),
      ),
    );
  }

  Widget _buildInputArea( context ) {
    final isLock = lockNearnCtrl.selected.value;
    final amountController = isLock ? lockNearnCtrl.amountLockEditor : lockNearnCtrl.amountUnLockEditor;

    return Obx(() {
      final hasPreview = lockNearnCtrl.unlockPreviewText.value.isNotEmpty;
      return Column(
        children: [
          SizedBox(
            height: 100,
            child: Center(
              child: AnimatedSwitcher(
                duration: Duration(milliseconds: 300),
                transitionBuilder: (child, animation) {
                  final slide = Tween<Offset>(
                    begin: Offset(1.0, 0.0),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

                  final fade = Tween<double>(begin: 0, end: 1).animate(animation);

                  return SlideTransition(
                    position: slide,
                    child: FadeTransition(opacity: fade, child: child),
                  );
                },
                child: hasPreview
                    ? Padding(
                  key: ValueKey('preview_text'),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    lockNearnCtrl.unlockPreviewText.value,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                )
                    : TextField(
                  key: ValueKey('amount_input'),
                  controller: lockNearnCtrl.amountUnLockEditor,
                  inputFormatters: [ThousandsFormatter()],
                  decoration: InputDecoration(
                    hintText: '0',
                    hintStyle: TextStyle(color: Colors.white30),
                    border: InputBorder.none,
                  ),
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white, fontSize: 36),
                  keyboardType: TextInputType.number,
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () async {
              lockNearnCtrl.changeOnTapUnlock();
              return;
              final checkShow = await lockNearnCtrl.checkUnlockConditions();
              if (checkShow) {
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (_) => WillPopScope(
                    onWillPop: () async => false,
                    child: Dialog(
                      elevation: 500,
                      backgroundColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30.0),
                      ),
                      child: Container(
                        height: mediaQuery(context, 'height', 554.63),
                        width: mediaQuery(context, 'width', 929.64),
                        margin: EdgeInsets.only(bottom: mediaQuery(context, 'height', 600)),
                        child: ClipRect(
                          child: BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                            child: Container(
                              decoration: BoxDecoration(
                                color: LikeWalletAppTheme.white.withOpacity(0.6),
                                borderRadius: BorderRadius.all(Radius.circular(20.0)),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: <Widget>[
                                  Text(
                                    'notify_title_notification'.tr,
                                    style: TextStyle(
                                      fontSize: mediaQuery(context, "height", 56),
                                      fontWeight: FontWeight.bold,
                                      color: LikeWalletAppTheme.black,
                                      fontFamily: 'Proxima Nova',
                                    ),
                                  ),
                                  SizedBox(height: mediaQuery(context, 'height', 40)),
                                  Text(
                                    'alert_lendex_borrow_unlock'.tr,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: mediaQuery(context, "height", 42),
                                      fontWeight: FontWeight.w500,
                                      color: LikeWalletAppTheme.black,
                                      fontFamily: 'Proxima Nova',
                                    ),
                                  ),
                                  Divider(color: LikeWalletAppTheme.black.withOpacity(0.4)),
                                  Row(
                                    children: <Widget>[
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () {
                                            // TODO: handle left button tap
                                          },
                                          child: Container(
                                            height: mediaQuery(context, 'height', 127.66),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              border: Border(
                                                right: BorderSide(color: LikeWalletAppTheme.black.withOpacity(0.4)),
                                              ),
                                            ),
                                            child: Text(
                                              'network_error_button'.tr,
                                              style: TextStyle(
                                                fontSize: mediaQuery(context, "height", 52),
                                                fontWeight: FontWeight.w600,
                                                color: LikeWalletAppTheme.bule1_7,
                                                fontFamily: 'Proxima Nova',
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () => Navigator.pop(context),
                                          child: Container(
                                            height: mediaQuery(context, 'height', 127.66),
                                            alignment: Alignment.center,
                                            child: Text(
                                              'logout_no'.tr,
                                              style: TextStyle(
                                                fontSize: mediaQuery(context, "height", 52),
                                                fontWeight: FontWeight.w600,
                                                color: LikeWalletAppTheme.bule1_7,
                                                fontFamily: 'Proxima Nova',
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }
            },
            child: Container(
              padding: EdgeInsets.only(top: 18),
              width: 30.w,
              child: Image.asset(LikeWalletImage.locklike_icon_unlock),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildConfirmUnlock () {
    return Column(
      children: [
        SizedBox(height: 0.013.sh),
        Container(
            alignment: Alignment.center,
            child: Column(
              children: <Widget>[
                Text(
                    lockNearnCtrl.amountUnLockText.isEmpty ? "0" : f
                      .format(double.parse(
                      lockNearnCtrl.amountUnLockText.replaceAll(',', '')))
                      .toString() +
                      " " +
                      'lock_symbol'.tr,
                  textAlign: TextAlign.right,
                  style: TextStyle(
                      shadows: [
                        Shadow(
                          blurRadius: 5.0,
                          color: LikeWalletAppTheme.black
                              .withOpacity(0.5),
                          offset: Offset(0.0, 0.0),
                        ),
                      ],
                      color: LikeWalletAppTheme.bule1,
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w300,
                      fontSize: 20.sp),
                ),
                Text(
                  'lock_detail_unlock'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      shadows: [
                        Shadow(
                          blurRadius: 5.0,
                          color: LikeWalletAppTheme.black
                              .withOpacity(0.5),
                          offset: Offset(0.0, 0.0),
                        ),
                      ],
                      color: LikeWalletAppTheme.bule1,
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w300,
                      fontSize: 20.sp),
                ),
              ],
            )),
        SizedBox(height: 0.016.sh),
        Container(
          padding: EdgeInsets.only(top: 18),
          width: 30.w,
          child: Image.asset(LikeWalletImage.locklike_icon_unlock),
        ),
        SizedBox(height: 0.07.sh),

        Container(
          width: double.infinity,
          height: 80.h,
          // color: Colors.blue,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                  onTap: () {
                    // setState(() {
                    //   onTapUnlock = false;
                    //   onTapUnlockAll = 'no';
                    //   // onTapUnlockAll = true;
                    // });
                    lockNearnCtrl.changeOnTapUnlock();
                  },
                  child: Container(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          Image.asset(
                            LikeWalletImage.icon_button_cancel,
                            height:
                            mediaQuery(context, 'height', 103),
                          ),
                          SizedBox(
                            width: mediaQuery(context, 'width', 20),
                          ),
                          Text(
                            'lock_button_cancel'.tr,
                            style: TextStyle(
                                color: LikeWalletAppTheme.bule1,
                                letterSpacing: 0.1,
                                fontFamily: 'Proxima Nova',
                                fontWeight: FontWeight.w300,
                                fontSize: mediaQuery(
                                    context, "height", 36)),
                          )
                        ],
                      ))),
              GestureDetector(
                  onTap: () {


                  },
                  child: Container(
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: <Widget>[
                          Text(
                            'lock_button_confirm'.tr,
                            style: TextStyle(
                                color: LikeWalletAppTheme.bule1,
                                letterSpacing: 0.1,
                                fontFamily: 'Proxima Nova',
                                fontWeight: FontWeight.w300,
                                fontSize: mediaQuery(
                                    context, "height", 36)),
                          ),
                          SizedBox(
                            width: mediaQuery(context, 'width', 20),
                          ),
                          Image.asset(
                            LikeWalletImage.icon_button_next,
                            height:
                            mediaQuery(context, 'height', 103),
                          ),
                        ],
                      ))),
            ],
          )
        ),
      ],
    );
  }

// ส่วน build ปุ่ม All
  Widget _buildSetAllButton(context) {
    return Obx(() => lockNearnCtrl.onTapUnlock.value
        ? Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap: () {
              lockNearnCtrl.onTapUnlock.value = false;
              // ไม่ต้องแก้อะไร ไม่ต้องเรียก action อื่น
            },
            child: Container(
              width: 60.w,
              height: 25.h,
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
              decoration: BoxDecoration(
                color: Color(0xff17171e),
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 6,
                      offset: Offset(0, 3))
                ],
              ),
              child: Center(
                child: Text(
                  'lock_button_all'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Color(0xff00ffff), fontSize: 12),
                ),
              ),
            ),
          ),
        ],
      ),
    )
        : SizedBox());
  }

  Widget _buildConfirmButtons() {
    return Obx(() => AnimatedSwitcher(
      duration: Duration(milliseconds: 300),
      transitionBuilder: (child, animation) {
        final offsetAnimation = Tween<Offset>(
          begin: Offset(1.0, 0.0), // จากขวาเข้า
          end: Offset.zero,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return SlideTransition(position: offsetAnimation, child: child);
      },
      child: lockNearnCtrl.showConfirmButtons.value
          ? Padding(
        key: ValueKey('confirm_buttons'), // ต้องใช้ key เพื่อให้ switch ทำงาน
        padding: EdgeInsets.symmetric(horizontal: 12.0),
        child: Row(
          children: [
            Expanded(
              child: Align(
                alignment: Alignment.centerLeft,
                child: TextButton(
                  onPressed: lockNearnCtrl.cancelAction,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(LikeWalletImage.icon_button_cancel, width: 30, height: 30),
                      SizedBox(width: 6),
                      Text('ยกเลิก', style: TextStyle(color: Colors.cyan)),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              child: Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: lockNearnCtrl.handleActionButton,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('ยืนยัน', style: TextStyle(color: Colors.cyan)),
                      SizedBox(width: 6),
                      Image.asset(LikeWalletImage.icon_button_next, width: 30, height: 30),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      )
          : SizedBox(
        key: ValueKey('empty_space'),
      ),
    ));
  }

  Widget _buildCarousel() {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Image.asset(LikeWalletImage.locklike_BG_slider, fit: BoxFit.cover, width: double.infinity, height: 140),
        CarouselSlider(
          options: CarouselOptions(height: 140, autoPlay: true, enlargeCenterPage: true, viewportFraction: 1),
          items: lockNearnCtrl.carouselImages.map((imgPath) {
            return Builder(
              builder: (BuildContext context) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.asset(imgPath, width: double.infinity),
                );
              },
            );
          }).toList(),
        ),
      ],
    );
  }
}


class ThousandsFormatter extends TextInputFormatter {
  final NumberFormat _formatter = NumberFormat("#,###");

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // Remove all non-digit characters
    String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.isEmpty) return newValue.copyWith(text: '');

    final number = int.parse(digitsOnly);
    final newString = _formatter.format(number);

    return TextEditingValue(
      text: newString,
      selection: TextSelection.collapsed(offset: newString.length),
    );
  }
}