import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/otherController/homeController.dart';
import 'package:likewallet/controller/scanController/scanController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/alert/alertPatchUpdate.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/home/<USER>';
import 'package:likewallet/view/navigationBar/curvedNavigationBar.dart';
import 'package:likewallet/view/newsPage/newsPage.dart';
import 'package:likewallet/view/notification/historyPage.dart';
import 'package:likewallet/view/profile/profile.dart';
import 'package:likewallet/view/scanPage/scanPage.dart';
import 'package:likewallet/test/shorebird_test_page.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

class MainScreen extends StatefulWidget {
  final int? selectPage;

  const MainScreen({super.key, this.selectPage});

  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  late int _page;
  final GlobalKey<CurvedNavigationBarState> _bottomNavigationKey = GlobalKey();

  final shorebirdCodePush = ShorebirdCodePush();

  final List<Widget> _pageOption = [
    const HomePage(), // หน้า Home
    NewsPage(), // หน้า News
    const HistoryPage(), // หน้า Notification
    Container(), // none-usable
  ];

  bool changeState = false;
  late AnimationController rotationController;
  Animation<double>? animation;

  late final homeCtrl;
  late final scanCtrl;

  @override
  void initState() {
    super.initState();
    // Initialize page from selectPage parameter if provided
    _page = widget.selectPage ?? 0;
    homeCtrl = Get.isRegistered<HomeController>() ? Get.find<HomeController>() : Get.put(HomeController());
    scanCtrl = Get.isRegistered<ScanController>() ? Get.find<ScanController>() : Get.put(ScanController());

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(Duration(milliseconds: 2000), () {

        _checkForUpdates(context);
      });
    });
  }

  Future<void> _checkForUpdates(BuildContext context) async {
    try {
      print("🔍 Starting update check...");

      // Check whether a patch is available to install.
      final isUpdateAvailable = await shorebirdCodePush.isNewPatchAvailableForDownload();

      print("📱 Update check result: $isUpdateAvailable");
      print("🏷️ Current patch number: ${await shorebirdCodePush.currentPatchNumber()}");
      // print("🆔 App ID: ${await shorebirdCodePush.appId()}");
      // print("🔢 Release version: ${await shorebirdCodePush.releaseVersion()}");

      if (isUpdateAvailable) {
        print("✅ Update available! Showing dialog...");
        // Show the update popup.

        showGeneralDialog(
          barrierLabel: "showGeneralDialog",
          barrierDismissible: true,
          barrierColor: Colors.black.withOpacity(0.6),
          transitionDuration: const Duration(milliseconds: 300),
          context: context,
          pageBuilder: (context, _, __) {
            return AlertUpdatePatchPage();
          },
          transitionBuilder: (_, animation1, __, child) {
            return SlideTransition(
              position: Tween(
                begin: const Offset(0, 1),
                end: const Offset(0, 0),
              ).animate(animation1),
              child: child,
            );
          },
        );
      } else {
        print("❌ No updates available");
      }
    } catch (e, stackTrace) {
      print("❌ Error checking for updates: $e");
      print("📍 Stack trace: $stackTrace");
    }
  }

  // Test method to manually trigger update check
  Future<void> _testUpdateCheck() async {
    print("🧪 Manual test triggered");
    await _checkForUpdates(context);
  }

  // Test method to force show update dialog (for UI testing)
  void _testShowUpdateDialog() {
    print("🧪 Forcing update dialog display");
    showDialog(
      context: context,
      useSafeArea: false,
      builder: (_) => AlertUpdatePatchPage(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: closeButtonPay,
      child: Scaffold(
        floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
        floatingActionButton: _page == 0 ? _buildPayButton() : const SizedBox(),
        body: Stack(
          children: [
            _pageOption[_page],
            // Debug buttons for testing (remove in production)
            if (false) // Set to false in production
              Positioned(
                top: 50,
                right: 10,
                child: Column(
                  children: [
                    ElevatedButton(
                      onPressed: _testUpdateCheck,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      ),
                      child: Text(
                        'Test Update Check',
                        style: TextStyle(fontSize: 10, color: Colors.white),
                      ),
                    ),
                    SizedBox(height: 5),
                    ElevatedButton(
                      onPressed: _testShowUpdateDialog,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      ),
                      child: Text(
                        'Force Show Dialog',
                        style: TextStyle(fontSize: 10, color: Colors.white),
                      ),
                    ),
                    SizedBox(height: 5),
                    ElevatedButton(
                      onPressed: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => ShorebirdTestPage()),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      ),
                      child: Text(
                        'Test Page',
                        style: TextStyle(fontSize: 10, color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            Positioned(
              bottom: 0,
              child: SizedBox(
                height: 80.h,
                width: MediaQuery.of(context).size.width,
                child: CurvedNavigationBar(
                  key: _bottomNavigationKey,
                  index: _page,
                  items: [
                    _buildNavItem(IconHome.path_43609, 0),
                    _buildNavItem(IconHome.path_43608, 1),
                    // _buildNotificationItem(),
                    _buildNavItem(IconHome.group_24548, 2),
                    GestureDetector(
                      onTap: () {
                        showDialog(
                            context: context,
                            useSafeArea: false,
                            builder: (context) => const ContactUsPage());
                      },
                        child: Container(child: _buildNavItem(IconHome.path_58781, 3),
                        ),
                    ),
                    // _buildChatItem(),
                  ],
                  color: colorNav() ? const Color(0xff141322) : Colors.white,
                  buttonBackgroundColor: colorNav() ? const Color(0xff141322) : Colors.white,
                  backgroundColor: Colors.transparent,
                  animationCurve: Curves.easeInOut,
                  animationDuration: const Duration(milliseconds: 300),
                  onTap: (index) {
                    print(index);
                    if (index == 3) {
                      // Get.to(() => ContactUsPage());
                    } else {
                      setState(() => _page = index);
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildPayButton() {
  //   return Stack(
  //     clipBehavior: Clip.none,
  //     children: [
  //       Positioned(
  //         bottom: 50.h,
  //         right: -30.w,
  //         child: Container(
  //           height: 200.h,
  //           width: 200.h,
  //           alignment: Alignment.center,
  //           child: Stack(
  //             clipBehavior: Clip.none,
  //             children: [
  //               GestureDetector(
  //                 onTap: () => print('click Main Pay Icon'),
  //                 child: AnimatedSwitcher(
  //                   duration: const Duration(milliseconds: 140),
  //                   transitionBuilder: (child, animation) => ScaleTransition(scale: animation, child: child),
  //                   child: Container(
  //                     key: const ValueKey(false),
  //                     child: Image.asset(
  //                       LikeWalletImage.icon_quick_pay,
  //                       height: 90.h,
  //                       width: 90.h,
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //               Positioned(
  //                 top: 70.h,
  //                 left: 10.w,
  //                 child: AnimatedSwitcher(
  //                   duration: const Duration(milliseconds: 140),
  //                   transitionBuilder: (child, animation) => ScaleTransition(scale: animation, child: child),
  //                   child: GestureDetector(
  //                     onTap: () => print('click Scan Pay'),
  //                     child: Container(
  //                       key: const ValueKey(true),
  //                       height: 35.h,
  //                       width: 35.h,
  //                       decoration: BoxDecoration(
  //                         shape: BoxShape.circle,
  //                         boxShadow: [
  //                           BoxShadow(
  //                             color: Colors.black.withOpacity(0.16),
  //                             offset: const Offset(0, 3),
  //                             blurRadius: 5.0,
  //                             spreadRadius: 1.0,
  //                           ),
  //                         ],
  //                       ),
  //                       child: Image.asset(
  //                         LikeWalletImage.image_quick_pay_scan,
  //                         height: 35.h,
  //                         width: 35.h,
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //               Positioned(
  //                 top: 5.h,
  //                 right: 40.w,
  //                 child: AnimatedSwitcher(
  //                   duration: const Duration(milliseconds: 140),
  //                   transitionBuilder: (child, animation) => ScaleTransition(scale: animation, child: child),
  //                   child: GestureDetector(
  //                     onTap: () => print('click Store Pay'),
  //                     child: Container(
  //                       key: const ValueKey(true),
  //                       height: 35.h,
  //                       width: 35.h,
  //                       decoration: BoxDecoration(
  //                         shape: BoxShape.circle,
  //                         boxShadow: [
  //                           BoxShadow(
  //                             color: Colors.black.withOpacity(0.16),
  //                             offset: const Offset(0, 3),
  //                             blurRadius: 5.0,
  //                             spreadRadius: 1.0,
  //                           ),
  //                         ],
  //                       ),
  //                       child: Image.asset(
  //                         LikeWalletImage.image_quick_pay_store,
  //                         height: 35.h,
  //                         width: 35.h,
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }

  Widget _buildPayButton() {
    return Stack(
      children: <Widget>[
        Positioned(
          bottom: mediaQuery(context, "height", 110),
          right: mediaQuery(context, "width", -70),
          child: Container(
//        color: Colors.lightBlue,
              margin: EdgeInsets.only(),
              alignment: Alignment.center,
              height: mediaQuery(context, "height", 450),
              width: mediaQuery(context, "height", 450),
              child: Stack(
                children: <Widget>[
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        if (changeState == false) {
                          changeState = true;
                          rotationController = AnimationController(
                            vsync: this,
                            duration: Duration(milliseconds: 140),
                          )..addListener(() => setState(() {}));
                          animation = CurvedAnimation(
                            parent: rotationController,
                            curve: Curves.easeInBack,
                          );
                          print(changeState);
                        } else {
                          changeState = false;
                          rotationController.forward();
                          print(changeState);
                        }
                      });
                    },
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 140),
                      transitionBuilder:
                          (Widget child, Animation<double> animation) {
                        return ScaleTransition(child: child, scale: animation);
                      },
                      child: changeState == false
                          ? Container(
                          key: ValueKey<bool>(changeState),
                          alignment: Alignment.center,
                          child: Image.asset(
                            LikeWalletImage.icon_quick_pay,
                            height: mediaQuery(context, "height", 210),
                            width: mediaQuery(context, "height", 210),
                          ))
                          : Container(
                          key: ValueKey<bool>(changeState),
                          alignment: Alignment.center,
                          child: Stack(
                            children: <Widget>[
                              RotationTransition(
                                turns: animation!,
                                child: Image.asset(
                                  LikeWalletImage.image_quick_pay,
                                  height:
                                  mediaQuery(context, "height", 350),
                                  width: mediaQuery(context, "height", 350),
                                ),
                              ),
                            ],
                          )),
                    ),
                  ),
                  Positioned(
                    top: mediaQuery(context, "height", 170),
                    left: mediaQuery(context, "width", 20),
                    child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 140),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return ScaleTransition(
                              child: child, scale: animation);
                        },
                        child: (changeState != false)
                            ? GestureDetector(
                          onTap: () async {
                            changeState = false;

                            print("scan");
                            await scanCtrl.setPage("home");

                            Get.to(() => ScanPage());
                          },
                          child: Container(
                            height: mediaQuery(context, "height", 81.87),
                            width: mediaQuery(context, "height", 81.87),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                new BoxShadow(
                                    color: Colors.black.withOpacity(0.16),
                                    offset: new Offset(0, 3),
                                    blurRadius: 5.0,
                                    spreadRadius: 1.0),
                              ],
                            ),
                            child: Image.asset(
                              LikeWalletImage.image_quick_pay_scan,
                              height:
                              mediaQuery(context, "height", 81.87),
                              width: mediaQuery(context, "height", 81.87),
                              key:
                              ValueKey<bool>(changeState),
                            ),
                          ),
                        )
                            : Container(
                          padding: EdgeInsets.only(
                            left: mediaQuery(context, "width", 230),
                          ),
                        )),
                  ),
                  Positioned(
                    top: mediaQuery(context, "height", 15),
                    right: mediaQuery(context, "width", 100),
                    child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 140),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return ScaleTransition(
                              child: child, scale: animation);
                        },
                        child: (changeState != false)
                            ? InkWell(
                          onTap: () {
                            changeState = false;

                            print("store");

                            homeCtrl.goToTab(1);

                          },
                          child: Container(
                            alignment: Alignment.topLeft,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                new BoxShadow(
                                    color: Colors.black.withOpacity(0.16),
                                    offset: new Offset(0, 3),
                                    blurRadius: 5.0,
                                    spreadRadius: 1.0),
                              ],
                            ),
                            child: Image.asset(
                              LikeWalletImage.image_quick_pay_store,
                              height:
                              mediaQuery(context, "height", 81.87),
                              width: mediaQuery(context, "height", 81.87),
                              key:
                              ValueKey<bool>(changeState),
                            ),
                          ),
                        )
                            : Container(
                          alignment: Alignment.topLeft,
                          padding: EdgeInsets.only(
                            top: mediaQuery(context, "height", 100),
                          ),
                        )),
                  )
                ],
              )),
        )
      ],
    );
  }

  Widget _buildNavItem(IconData icon, int index) {
    return SizedBox(
      height: 50.h,
      width: 50.h,
      child: Icon(
        icon,
        size: 24.h,
        color: _page == 0 ? Colors.white : Colors.black,
      ),
    );
  }

  // Widget _buildNotificationItem() {
  //   return SizedBox(
  //     height: 50.h,
  //     width: 50.h,
  //     child: StreamBuilder<QuerySnapshot>(
  //       stream: fireStore
  //           .collection('notificationByUser')
  //           .doc(uid)
  //           .collection('notify')
  //           .where("status", isEqualTo: "unread")
  //           .snapshots(),
  //       builder: (context, snapshot) {
  //         if (snapshot.hasError || snapshot.connectionState == ConnectionState.waiting) {
  //           return Icon(
  //             IconHome.path_43608,
  //             size: 28.h,
  //             color: _page == 1 ? Colors.white : Colors.black,
  //           );
  //         }
  //         final count = snapshot.data!.docs.length;
  //         return Stack(
  //           alignment: Alignment.center,
  //           children: [
  //             Icon(
  //               IconHome.path_43608,
  //               size: 28.h,
  //               color: _page == 1 ? Colors.white : Colors.black,
  //             ),
  //             if (count > 0)
  //               Positioned(
  //                 top: 5.h,
  //                 right: 5.w,
  //                 child: Container(
  //                   height: 20.h,
  //                   width: 20.h,
  //                   alignment: Alignment.center,
  //                   decoration: const BoxDecoration(
  //                     shape: BoxShape.circle,
  //                     color: Color(0xffFFC400),
  //                   ),
  //                   child: Text(
  //                     count.toString(),
  //                     style: TextStyle(
  //                       fontFamily: 'Proxima Nova',
  //                       fontSize: 12.sp,
  //                       color: Colors.white,
  //                       fontWeight: FontWeight.w500,
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //           ],
  //         );
  //       },
  //     ),
  //   );
  // }
  //
  // Widget _buildChatItem() {
  //   return SizedBox(
  //     height: 50.h,
  //     width: 50.h,
  //     child: StreamBuilder<QuerySnapshot>(
  //       stream: fireStore
  //           .collection('messages')
  //           .doc(uid)
  //           .collection('messages')
  //           .where("status", isEqualTo: "unread")
  //           .snapshots(),
  //       builder: (context, snapshot) {
  //         if (snapshot.hasError || snapshot.connectionState == ConnectionState.waiting) {
  //           return Icon(
  //             IconHome.path_58781,
  //             size: 28.h,
  //             color: _page == 3 ? Colors.white : Colors.black,
  //           );
  //         }
  //         final count = snapshot.data!.docs.length;
  //         return Stack(
  //           alignment: Alignment.center,
  //           children: [
  //             Icon(
  //               IconHome.path_58781,
  //               size: 28.h,
  //               color: _page == 3 ? Colors.white : Colors.black,
  //             ),
  //             if (count > 0)
  //               Positioned(
  //                 top: 5.h,
  //                 right: 5.w,
  //                 child: Container(
  //                   height: 20.h,
  //                   width: 20.h,
  //                   alignment: Alignment.center,
  //                   decoration: const BoxDecoration(
  //                     shape: BoxShape.circle,
  //                     color: Color(0xffFFC400),
  //                   ),
  //                   child: Text(
  //                     count.toString(),
  //                     style: TextStyle(
  //                       fontFamily: 'Proxima Nova',
  //                       fontSize: 12.sp,
  //                       color: Colors.white,
  //                       fontWeight: FontWeight.w500,
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //           ],
  //         );
  //       },
  //     ),
  //   );
  // }

  colorNav() {
    return _page == 0 || _page == 3 ? true : false;
  }

  void closeButtonPay() {
    // TODO: เพิ่ม logic สำหรับปิดปุ่มจ่ายเงิน
  }
}// Test change made at: 2025-05-29 15:49:59
// Test change made at: 2025-06-03 13:58:36
