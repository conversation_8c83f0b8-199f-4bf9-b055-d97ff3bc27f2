import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:likewallet/controller/otherController/configurationController.dart';
import 'package:likewallet/controller/rewardController/rewardController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/loading.dart';
import 'package:likewallet/view/reward/blinkButton.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../controller/rewardCurrency/rewardCurrencyController.dart';

class Rewards extends StatefulWidget {
  const Rewards({Key? key}) : super(key: key);

  @override
  _RewardsState createState() => _RewardsState();
}

class _RewardsState extends State<Rewards> {
  // Get the reward controller
  final RewardController rewardCtrl = Get.find<RewardController>();
  final ConfigurationController configCtrl = Get.put(ConfigurationController());
  final RewardCurrencyController rewardCurrencyCtrl = Get.put(RewardCurrencyController());

  @override
  Widget build(BuildContext context) {
    return Obx(() => ModalProgressHUD(
      inAsyncCall: rewardCtrl.isLoading.value,
      opacity: 0.1,
      progressIndicator: CustomLoading(),
      child: Scaffold(
        backgroundColor: Color(0xFF161329),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              height: 0.05.sh,
            ),
            Container(
              width: 0.85.sw,
              alignment: Alignment.centerLeft,
              child: InkWell(
                onTap: () {
                  print("object");
                  // Navigate back to the previous screen
                  Get.back();
                },
                child: Container(
                  width: 50.w,
                  height: 50.h,
                  alignment: Alignment.center,
                  child: Image.asset(
                    LikeWalletImage.icon_back_button,
                    height: 20.h,
                    color: LikeWalletAppTheme.gray,
                  ),
                ),
              ),
            ),
            SizedBox(
              height:  0.13.sh,
            ),

            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Next rewards section
                Container(
                  alignment: Alignment.centerLeft,
                  padding: EdgeInsets.only(left: 24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        'hourlyRewards_title1'.tr,
                        style: TextStyle(
                          fontFamily: "Proxima Nova",
                          fontSize: 16.sp,
                          color: const Color(0xb252ffff).withOpacity(0.6),
                          letterSpacing: 0.5,
                          height: 1.2,
                        ),
                        textAlign: TextAlign.left,
                      ),
                      SizedBox(height: 5.h),
                      Text(
                        rewardCtrl.f.format(rewardCtrl.nextRewards.value).toString(),
                        style: TextStyle(
                          fontFamily: "Proxima Nova",
                          fontSize: 20.sp,
                          color: const Color(0xb252ffff).withOpacity(0.6),
                          letterSpacing: 0.5.w,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ],
                  ),
                ),

                // Info button
                InkWell(
                  onTap: () async {
                    // Show info dialog or navigate to info screen
                    // Get.dialog(
                    //   AlertDialog(
                    //     title: Text('Rewards Information'),
                    //     content: Text('This is your hourly rewards information. Claim your rewards every hour to maximize your earnings.'),
                    //     actions: [
                    //       TextButton(
                    //         onPressed: () => Get.back(),
                    //         child: Text('OK'),
                    //       ),
                    //     ],
                    //   ),
                    // );


                    // await configCtrl.getPrivateKey();
                    await rewardCtrl.getNewReward();

                    // print(key);
                  },
                  child: Container(
                    width: 30.w,
                    height: 35.h,
                    // padding: EdgeInsets.only(top: 8.h, bottom: 8.h),
                    // alignment: Alignment.topCenter,
                    child: SvgPicture.string(
                      '<svg viewBox="507.0 888.0 65.9 33.4" ><path transform="translate(242.47, 484.07)" d="M 330.4030151367188 405.9169921875 C 330.1220092773438 404.5660095214844 328.7980041503906 403.697998046875 327.4460144042969 403.97900390625 L 317.4020080566406 406.0679931640625 C 316.8819885253906 406.1759948730469 316.4100036621094 406.4469909667969 316.0539855957031 406.8420104980469 C 315.1300048828125 407.8680114746094 315.2130126953125 409.4490051269531 316.239013671875 410.3729858398438 L 319.2720031738281 413.10400390625 C 319.1499938964844 413.1849975585938 319.0320129394531 413.2730102539062 318.9230041503906 413.3770141601562 L 309.3269958496094 422.5969848632812 L 293.0740051269531 409.2040100097656 L 265.406005859375 432.9039916992188 C 264.3569946289062 433.8030090332031 264.2349853515625 435.3810119628906 265.1340026855469 436.4289855957031 C 265.6279907226562 437.0069885253906 266.3290100097656 437.3030090332031 267.0329895019531 437.3030090332031 C 267.6090087890625 437.3030090332031 268.18701171875 437.1050109863281 268.6589965820312 436.7009887695312 L 293.135986328125 415.7340087890625 L 309.5830078125 429.2860107421875 L 322.3880004882812 416.9830017089844 C 322.5880126953125 416.7900085449219 322.7380065917969 416.5679931640625 322.8599853515625 416.3349914550781 L 325.2550048828125 418.4909973144531 C 325.6499938964844 418.8469848632812 326.1489868164062 419.0679931640625 326.6780090332031 419.1210021972656 C 328.052001953125 419.2590026855469 329.2770080566406 418.2569885253906 329.4150085449219 416.8829956054688 L 330.4419860839844 406.677001953125 C 330.4679870605469 406.4230041503906 330.4549865722656 406.1669921875 330.4030151367188 405.9169921875 Z" fill="#0fe8d8" stroke="none" stroke-width="1" stroke-miterlimit="10" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                    ),
                  ),
                ),

                // Main content container
                Container(
                  height: MediaQuery.of(context).size.height * 0.66,
                  child: Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      // Detail text
                      Positioned(
                        bottom: 60.h,
                        width: MediaQuery.of(context).size.width * 0.8,
                        child: Text(
                          'hourlyRewards_detail'.tr,
                          style: TextStyle(
                            fontFamily: "Proxima Nova",
                            fontSize: 12.sp,
                            color: const Color(0xff7b75fd),
                            fontWeight: FontWeight.w300,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),

                      // Main body content
                      Positioned(
                        top: 60.h,
                        child: _buildBody(),
                      ),

                      // Timer at the top
                      Positioned(
                        top: 15.h,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(25.0),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.16),
                                offset: Offset(0, 10.sp),
                                blurRadius: 15.sp,
                              ),
                            ],
                          ),
                          child: _buildTimer(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    ));
  }

  Widget _buildBody() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.9,
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, 1.0),
          colors: [const Color(0xff273352), const Color(0x00273352)],
          stops: [0.0, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0x1a000000),
            offset: Offset(0, -3),
            blurRadius: 12,
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(height: 80.h),

          // Your claim text
          Text(
            'hourlyRewards_your_claim'.tr,
            style: TextStyle(
              fontFamily: "Proxima Nova",
              fontSize: 18.sp,
              color: const Color(0xff0fe8d8),
              letterSpacing: 0.5.w,
              fontWeight: FontWeight.w300,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 20.h),

          // Reward amount
          Obx(() => Text(
            rewardCtrl.rewards.value == 0
                ? '0'
                : rewardCtrl.fdecimal.format(rewardCtrl.rewards.value),
            style: TextStyle(
              fontFamily: 'Roboto',
              fontSize: 32.sp,
              color: const Color(0xff52ffff),
              fontWeight: FontWeight.w500,
              height: 1.2,
            ),
            textAlign: TextAlign.center,
          )),
          SizedBox(height: 20.h),


          // Currency type indicator
          Obx(() => Text(
            '${rewardCurrencyCtrl.getCurrentRewardCurrencyLabel()}',
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 16.sp,
              color: LikeWalletAppTheme.gray2,
              fontWeight: FontWeight.w400,
            ),
          )),

          SizedBox(height: 20.h),


          // Currency type indicator
          Obx(() => rewardCtrl.rewards.value == 0
              ? _buildUnclaimButton()
              : _buildClaimButton()
          ),

          SizedBox(height: 15.h),
          SizedBox(height: 15.h),

          // Annual income or claim button
          // Obx(() => rewardCtrl.buttonClaimAndLock.value == 2
          //   ? MyBlinkingButton(Income: rewardCtrl.income.value)
          //   :
          // // rewardCtrl.buttonClaimAndLock.value != 2 &&
          // //     rewardCtrl.isClaim.value != ClaimStatus.INACTIVE &&
          //     rewardCtrl.rewards.value != 0
          //     ? _buildClaimButton()
          //     : Container()
          // ),

          // _buildClaimButton(),

          SizedBox(height: 34.h),

          // Show income when claim is successful
          // Obx(() => rewardCtrl.buttonClaim.value == 2
          //   ? MyBlinkingButton(Income: rewardCtrl.income.value)
          //   : Container()
          // ),
        ],
      ),
    );
  }

  Widget _buildUnclaimButton() {
    return Container(
      alignment: Alignment.center,
      child: ButtonTheme(
        minWidth: MediaQuery.of(context).size.width * 0.8,
        height: 50.h,
        child: TextButton(
          style: ButtonStyle(
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                if (states.contains(MaterialState.disabled)) {
                  return Color(0xff56596C); // Disabled color
                }
                return Color(0xff56596C); // Regular color
              },
            ),
          ),
          onPressed: () {
            // Refresh rewards data
            // rewardCtrl.fetchRewardData();
          },
          child: Text(
            'hourlyRewards_button_unclaimed'.tr,
            style: TextStyle(
              fontSize: 14.sp,
              color: Color(0xff262730),
              fontWeight: FontWeight.bold,
              fontFamily: 'Proxima Nova',
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClaimButton() {
    return InkWell(
      onTap: () async {
        try{
          // Show loading indicator
          rewardCtrl.isLoading.value = true;

          // Attempt to claim rewards
          await rewardCtrl.claimRewards();

          // Hide loading indicator
          rewardCtrl.isLoading.value = false;

          Get.snackbar(
            'getDailyReward'.tr,
            rewardCtrl.fdecimal.format(rewardCtrl.rewardsRecieve.value).toString(),
            backgroundColor: Color(0xFF8CF9F0),
          );
        }catch(e){
          print("Error: $e");
        }

      },
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        width: MediaQuery.of(context).size.width * 0.8,
        height: 50.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          gradient: LinearGradient(
            begin: Alignment(-1.0, -0.09),
            end: Alignment(1.01, 0.0),
            colors: [
              const Color(0xFF8CF9F0),
              const Color(0xFF5AB8D8)
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x59000000),
              offset: Offset(0, 3),
              blurRadius: 8,
            ),
          ],
        ),
        child: Center(
          child: Text(
            'hourlyRewards_button_claim'.tr,
            style: TextStyle(
              fontSize: 14.sp,
              color: Color(0xff262730),
              fontWeight: FontWeight.bold,
              fontFamily: 'Proxima Nova',
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimer() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Background container
        Container(
          alignment: Alignment.topCenter,
          height: 90.h,
          width: MediaQuery.of(context).size.width * 0.8,
          child: SvgPicture.string(
            '<svg viewBox="0.0 0.0 884.0 240.0" ><defs><filter id="shadow"><feDropShadow dx="0" dy="24" stdDeviation="35"/></filter><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#ff222541"  /><stop offset="1.0" stop-color="#ff1d2038"  /></linearGradient></defs><path  d="M 120 0 L 764 0 C 830.274169921875 0 884 53.725830078125 884 120 C 884 186.274169921875 830.274169921875 240 764 240 L 120 240 C 53.725830078125 240 0 186.274169921875 0 120 C 0 53.725830078125 53.725830078125 0 120 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/></svg>',
            allowDrawingOutsideViewBox: true,
            fit: BoxFit.fill,
          ),
        ),

        // Timer text
        Column(
          children: [
            Text(
              'Time Left Until Next Reward (11:00 AM)',
              style: TextStyle(
                fontFamily: "Proxima Nova",
                fontSize: 14.sp,
                color: const Color(0xff7b75fd),
                letterSpacing: 1.w,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 10.h),
            Obx(() => Text(
              "${rewardCtrl.rewardTime.value}",
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                fontSize: 20.sp,
                color: const Color(0xffffffff),
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.left,
            )),
          ],
        ),

        // Clock icon
        Positioned(
          right: 30.w,
          bottom: -10.h,
          width: 60.w,
          height: 60.h,
          child: Image.asset(
            LikeWalletImage.icon_time,
            fit: BoxFit.contain,
          ),
        ),
      ],
    );
  }

  // Optional: Show reward animation
  Widget _buildRewardAnimation() {
    return Obx(() => Container(
      child: Lottie.network(
        rewardCtrl.image.value,
        fit: BoxFit.fill,
        height: MediaQuery.of(context).size.height * 0.3,
        width: MediaQuery.of(context).size.width,
      ),
    ));
  }
}
