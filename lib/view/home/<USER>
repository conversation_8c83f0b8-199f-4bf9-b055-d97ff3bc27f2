import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/walletController/walletDataController.dart';
import 'package:likewallet/view/alert/alert.dart';
import 'package:likewallet/view/alert/alertPatchUpdate.dart';
import 'package:likewallet/view/like2crypto/popupKYC.dart';

class HomeHeadScreen extends StatefulWidget {
  const HomeHeadScreen({super.key});

  @override
  State<HomeHeadScreen> createState() => _HomeHeadScreenState();
}

class _HomeHeadScreenState extends State<HomeHeadScreen> {

  final WalletDataController walletController = Get.find<WalletDataController>();
  final ProfileController profileController = Get.find<ProfileController>();

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TabBar(
                padding: EdgeInsets.only(left: 14.w),
                labelColor: Colors.black,
                tabAlignment: TabAlignment.start,
                labelStyle: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontWeight: FontWeight.w700,
                  fontSize: 20.sp,
                ),
                unselectedLabelColor: Colors.black.withOpacity(0.4),
                unselectedLabelStyle: TextStyle(
                  fontFamily: 'Proxima Nova',
                  fontWeight: FontWeight.normal,
                  fontSize: 17.sp,
                ),
                dividerColor: Colors.transparent,
                indicatorColor: Colors.transparent,
                indicator: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 2.0,
                    ),
                  ),
                ),
                isScrollable: true,
                tabs: [
                  _buildTab(context, 'home_Total'.tr, 0),
                  _buildTab(context, 'home_Locked'.tr, 1),
                  _buildTab(context, 'home_Available'.tr, 2),
                ],
              ),
              Container(
                padding: EdgeInsets.only(right: 14.w),
                child: GestureDetector(
                  onTap: () async {
                    var userPhone = profileController.user?.phoneNumber;
                    walletController.getDataWallet(userPhone);
                  },
                  child: Container(
                    height: 35.h,
                    margin: EdgeInsets.only(right: 6.w),
                    child: SvgPicture.asset(
                      'assets/svgs/refresh.svg',
                      color: Colors.black,
                      height: 18.h,
                    ),
                  ),
                ),
              ),
            ],
          ),
          Container(
            height: 117.h,
            width: double.infinity,
            alignment: Alignment.topCenter,
            child: Stack(
              alignment: Alignment.topCenter,
              children: [
                Opacity(
                  opacity: 0.6,
                  child: Container(
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xff00C4D5).withOpacity(0.3),
                          offset: Offset(0, -4.h),
                          spreadRadius: 0,
                          blurRadius: 20.h,
                        ),
                      ],
                    ),
                    child: SvgPicture.string(
                      '<svg viewBox="0.0 289.0 1084.0 232.0"><defs><filter id="shadow"><feDropShadow dx="0" dy="-9" stdDeviation="45"/></filter><linearGradient id="gradient" x1="0.0" y1="0.5" x2="1.0" y2="0.5"><stop offset="0.0" stop-color="#ffffffff" /><stop offset="1.0" stop-color="#1affffff" stop-opacity="0.1" /></linearGradient></defs><path transform="translate(0.0, 288.97)" d="M 51.07308197021484 0 L 1032.927001953125 0 C 1061.134033203125 0 1084 0 1084 0 L 1084 232.0312347412109 C 1084 232.0312347412109 1061.134033203125 232.0312347412109 1032.927001953125 232.0312347412109 L 51.07308197021484 232.0312347412109 C 22.86619567871094 232.0312347412109 -1.000192195732552e-08 232.0312347412109 -1.000192195732552e-08 232.0312347412109 L -1.000262361827708e-08 0 C -1.000262361827708e-08 0 22.86619567871094 0 51.07308197021484 0 Z" fill="url(#gradient)" fill-opacity="0.37" stroke="none" stroke-width="1" stroke-opacity="0.37" stroke-miterlimit="4" stroke-linecap="butt" filter="url(#shadow)"/>',
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
                TabBarView(
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildTotalTab(context),
                    _buildLockedTab(context),
                    _buildAvailableTab(context),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildTab(BuildContext context, String label, int index) {
    return Container(
      height: 35.h,
      alignment: Alignment.bottomCenter,
      padding: EdgeInsets.only(bottom: 4.h),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              fontSize: 15.sp,
            ),
          ),
          // if (index == 0) // Mock active tab
          //   Positioned(
          //     bottom: -4.h,
          //     child: Container(
          //       width: 20.w,
          //       height: 5.h,
          //       decoration: BoxDecoration(
          //         color: Colors.black,
          //         borderRadius: BorderRadius.circular(50),
          //       ),
          //     ),
          //   ),
        ],
      ),
    );
  }

  Widget _buildTotalTab(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w),
      child: Obx(() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 8.h),
          Text(
            '= THB ${formatNumber(walletController.totalBalanceTHB.value)}',
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              color: Colors.black.withOpacity(0.4),
              fontSize: 16.sp,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                formatNumber(walletController.totalBalance.value), // Mock data
                style: TextStyle(
                  fontFamily: 'Proxima Nova',
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                  fontSize: 28.sp,
                  height: 1.2,
                ),
              ),
              SizedBox(width: 15.w),
              GestureDetector(
                onTap: () => print('click LIKE Total'),
                child: Text(
                  'LIKE',
                  style: TextStyle(
                    fontFamily: 'Proxima Nova',
                    color: Colors.black.withOpacity(0.4),
                    fontSize: 14.sp,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ],
          ),
        ],
      )),
    );
  }

  Widget _buildLockedTab(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w),
      child: Obx(() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 8.h),
          Text(
            '= THB ${formatNumber(walletController.lockedBalanceTHB.value)}', // Mock data
            style: TextStyle(
              fontFamily: 'Proxima Nova',
              color: Colors.black.withOpacity(0.4),
              fontSize: 16.sp,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    formatNumber(walletController.lockedBalance.value), // Mock data
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black,
                      fontSize: 28.sp,
                      height: 1.2,
                    ),
                  ),
                  SizedBox(width: 25.w),
                  Text(
                    'LIKE',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black.withOpacity(0.4),
                      fontSize: 14.sp,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),

              Container(
                padding: EdgeInsets.only(right: 20.w),
                child: GestureDetector(
                  onTap: () => print('click Lock Detail'),
                  child: Icon(
                    Icons.read_more,
                    color: Colors.black,
                    size: 22.sp,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      ),
    );
  }

  Widget _buildAvailableTab(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Obx(() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    formatNumber(walletController.availableBalance.value), // Mock data
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black,
                      fontSize: 28.sp,
                      height: 1.2,
                    ),
                  ),
                  SizedBox(width: 18.w),
                  Text(
                    'LIKE',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black,
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      showDialog(
                        context: context,
                        useSafeArea: false,
                        builder: (_) => AlertUpdate(),
                      );
                      // showDialog(
                      //   context: context,
                      //   useSafeArea: false,
                      //   builder: (_) => AlertUpdatePatchPage(),
                      // );
                    },
                    child: Container(
                      color: Colors.red.withOpacity(0.5),
                      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                      child: Text(
                        'Test Update',
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          color: Colors.white,
                          fontSize: 12.sp,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  GestureDetector(
                    onTap: () {
                      showDialog(
                          context: context,
                          useSafeArea: false,
                          builder: (_) => PopupKYC()
                      );
                    },
                    child: Container(
                      color: Colors.red.withOpacity(0.5),
                      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                      child: Text(
                        'Test KYC',
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          color: Colors.white,
                          fontSize: 12.sp,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  GestureDetector(
                    onTap: () => print('click Available Page'),
                    child: Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Colors.black.withOpacity(0.6),
                      size: 18.sp,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bitcoin',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black.withOpacity(0.5),
                      fontSize: 15.sp,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Gold',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black.withOpacity(0.5),
                      fontSize: 15.sp,
                    ),
                  ),
                ],
              ),
              SizedBox(width: 12.w),
              Column(
                children: [
                  Text(
                    '=',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black.withOpacity(0.5),
                      fontSize: 15.sp,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '=',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black.withOpacity(0.5),
                      fontSize: 15.sp,
                    ),
                  ),
                ],
              ),
              SizedBox(width: 12.w),
              Column(
                children: [
                  Text(
                    '0.05472',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black.withOpacity(0.5),
                      fontSize: 15.sp,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '4.75472',
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      color: Colors.black.withOpacity(0.5),
                      fontSize: 15.sp,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      )),
    );
  }

  String formatNumber(double number) {
    return NumberFormat('#,##0.00').format(number);
  }
}