import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:likewallet/controller/otherController/drawerController.dart';
import 'package:likewallet/view/MVP/webOpenUrl.dart';
import 'package:likewallet/view/feedback/feedback.dart';
import 'package:likewallet/view/profile/profile.dart';
import 'package:likewallet/view/termAndPolicy/policyDialog.dart';
import 'package:likewallet/view/termAndPolicy/termConditionDialog.dart';
import 'dart:ui';
import 'package:url_launcher/url_launcher.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:likewallet/controller/translate/translation_key.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/view/contactUs/contactUsPage.dart';
import 'package:likewallet/view/rewardCurrency/rewardCurrencyPage.dart';
import 'package:likewallet/controller/rewardCurrency/rewardCurrencyController.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;

import '../../controller/profile/profileController.dart';

class CustomSwitchTile extends StatefulWidget {
  final String title;
  final bool initialValue;
  final void Function(bool)? onChanged;

  const CustomSwitchTile({
    super.key,
    required this.title,
    this.initialValue = false,
    this.onChanged,
  });

  @override
  State<CustomSwitchTile> createState() => _CustomSwitchTileState();
}

class _CustomSwitchTileState extends State<CustomSwitchTile> {
  late bool _value;


  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                fontFamily: 'Proxima-Nova',
                fontSize: 14.sp,
                color: const Color(0xFF515057),
              ),
            ),
          ),
          Switch(
            value: _value,
            onChanged: (newValue) {
              setState(() {
                _value = newValue;
              });
              if (widget.onChanged != null) widget.onChanged!(newValue);
            },
            activeTrackColor: const Color(0xff0FE8D8).withOpacity(0.8),
            inactiveThumbColor: const Color(0xff929194),
            activeColor: const Color(0xff0FE8D8),
            inactiveTrackColor: const Color(0xff707070),
          ),
        ],
      ),
    );
  }
}

class AppDrawer extends StatelessWidget {
  AppDrawer({super.key});
  final Dio dio = Dio(); // เพิ่มตรงนี้ใน AppDrawer class

  String url_facebook = 'https://www.facebook.com/Likewallet-111904457115522';
  String url_twitter = 'https://twitter.com/LikewalletO';
  String url_youtube =
      'https://www.youtube.com/channel/UCvj2ILK4psSSSrOtfr9bDKg';

  final ValueNotifier<String> currencyNotifier =
  ValueNotifier(GetStorage().read('selected_currency') ?? 'thb');

  final List<Map<String, String>> languages = [
    {"code": "en", "label": "${language_us.tr}"},
    {"code": "th", "label": "${language_thai.tr}"},
    {"code": "lo", "label": "${language_lao.tr}"},
    {"code": "km", "label": "${language_cam.tr}"},
  ];

  final List<Map<String, String>> currencies = const [
    {"key": "usd", "label": "USD - US dollar"},
    {"key": "thb", "label": "บาท - ไทยบาท"},
    {"key": "lak", "label": "ກີບ - ລາວກີບ"},
    {"key": "vnd", "label": "₫ - Vietnamese Dong"},
  ];

  ProfileController profileCtrl = Get.find<ProfileController>();
  DrawerOwnController drawerCtrl = Get.isRegistered<DrawerOwnController>() ? Get.find<DrawerOwnController>() : Get.put(DrawerOwnController());
  final storage = GetStorage();

  void _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      Get.snackbar(
        'ไม่สามารถเปิดลิงก์ได้',
        url,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void dialogContent(BuildContext context) {
    final currentLocale = Get.locale?.languageCode ?? 'th';

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'dismiss',
      barrierColor: Colors.black.withOpacity(0.7),
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (_, __, ___) {
        return Material(
          color: const Color(0xff1e1d34),
          child: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding:
                  EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
                  child: Row(
                    children: [
                      backButton(context, LikeWalletAppTheme.white),
                      SizedBox(width: 8.w),
                      Text(
                        'tabslide_language'.tr,
                        style: TextStyle(
                          fontFamily: 'ProximaNova',
                          fontSize: 24.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(color: Colors.white24),
                Expanded(
                  child: ListView(
                    children: languages.map((lang) {
                      return _languageTile(
                        context,
                        lang['label']!.tr,
                        lang['code']!,
                        currentLocale,
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void currencyDialog(BuildContext context) {
    String selectedCurrency = currencyNotifier.value;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'currency',
      barrierColor: Colors.black.withOpacity(0.7),
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (_, __, ___) {
        return Material(
          color: const Color(0xff1e1d34),
          child: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding:
                  EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
                  child: Row(
                    children: [
                      backButton(context, LikeWalletAppTheme.white),
                      SizedBox(width: 8.w),
                      Text(
                        'tabslide_currency'.tr,
                        style: TextStyle(
                          fontFamily: 'ProximaNova',
                          fontSize: 24.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(color: Colors.white24),
                Expanded(
                  child: ListView(
                    children: currencies.map((curr) {
                      return InkWell(
                        onTap: () {
                          GetStorage().write('selected_currency', curr['key']);
                          currencyNotifier.value = curr['key']!;
                          Navigator.of(context).pop(); // ปิด dialog
                          print("เลือก currency: ${curr['key']}"); // << เพิ่มตรงนี้!
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.w, vertical: 16.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                curr['label']!,
                                style: TextStyle(
                                  fontFamily: 'ProximaNova',
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                ),
                              ),
                              if (curr['key'] == selectedCurrency)
                                const Icon(Icons.check,
                                    color: Colors.white, size: 20),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void showPrivacyBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xff1e1d34),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                contentPadding: EdgeInsets.zero,
                title: Text(
                  'tabslide_terms_and_conditions'.tr,
                  style: TextStyle(
                    fontFamily: 'ProximaNova',
                    fontSize: 16.sp,
                    color: Colors.white,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  showDialog(
                    context: context,
                    builder: (_) => TermsAndConditionsDialog(),
                  );
                },
              ),
              Divider(color: Colors.white24),
              ListTile(
                contentPadding: EdgeInsets.zero,
                title: Text(
                  "tabslide_privacy_policy".tr,
                  style: TextStyle(
                    fontFamily: 'ProximaNova',
                    fontSize: 16.sp,
                    color: Colors.white,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  showDialog(
                    context: context,
                    builder: (_) => PolicyDialog(),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void showMvpListDialog(BuildContext context) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'MVP List',
      barrierColor: Colors.black.withOpacity(0.7),
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (_, __, ___) {
        return Material(
          color: const Color(0xff1e1d34),
          child: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding:
                  EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
                  child: Row(
                    children: [
                      backButton(context, LikeWalletAppTheme.white),
                      SizedBox(width: 8.w),
                      Text(
                        'MVP LIST',
                        style: TextStyle(
                          fontFamily: 'ProximaNova',
                          fontSize: 24.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(color: Colors.white24),
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.all(16.w),
                    children: [
                      _mvpButtonFromFirebase(context, 'LOCK+'),
                      _mvpButtonFromFirebase(context, 'LPCU'),
                      _mvpButtonFromFirebase(context, 'RANKING'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void showPopup(BuildContext context, Widget Function(BuildContext) builder) {
    final newContext = Navigator.of(context, rootNavigator: true).context;

    showGeneralDialog(
      context: newContext,
      barrierDismissible: true,
      barrierLabel: 'popup',
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 600),
      transitionBuilder: (context, anim1, anim2, child) {
        return ScaleTransition(
          scale: CurvedAnimation(
            parent: anim1,
            curve: Curves.easeOutBack,
          ),
          child: FadeTransition(
            opacity: anim1,
            child: child,
          ),
        );
      },
      pageBuilder: (_, __, ___) => builder(newContext),
    );
  }

  Widget _mvpButtonFromFirebase(BuildContext context, String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: InkWell(
        onTap: () async {
          try {
            var token = await profileCtrl.getTokenFirebase();
            final response = await http.post(
              Uri.parse("https://new.likepoint.io/createCustomToken"),
              headers: {'Content-Type': 'application/json'},
              body: jsonEncode({"token": token}),
            );

            final data = jsonDecode(response.body);
            if (response.statusCode == 200 &&
                data["statusCode"] == 200 &&
                data["token"] != null) {
              final token = data["token"];

              final snapshot = await FirebaseFirestore.instance
                  .collection('mvpList')
                  .where('title', isEqualTo: title)
                  .limit(1)
                  .get();

              if (snapshot.docs.isNotEmpty) {
                final doc = snapshot.docs.first.data();
                final fullUrl = doc['url'] + token + (doc['path'] ?? '');
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => WebOpenMVP(url: fullUrl),
                  ),
                );
              } else {
                Get.snackbar("ไม่พบ URL", "Firebase ไม่มีรายการชื่อ $title",
                    backgroundColor: Colors.red, colorText: Colors.white);
              }
            } else {
              Get.snackbar("Token ผิดพลาด", data.toString(),
                  backgroundColor: Colors.red, colorText: Colors.white);
            }
          } catch (e) {
            print("❌ Error $e");
            Get.snackbar("Error", e.toString(),
                backgroundColor: Colors.red, colorText: Colors.white);
          }
        },
        child: Container(
          height: 60.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: Colors.black),
          ),
          child: Text(
            title,
            style: TextStyle(fontSize: 16.sp, color: Colors.black),
          ),
        ),
      ),
    );
  }

  Widget _languageTile(
      BuildContext context, String title, String code, String currentCode) {
    return InkWell(
      onTap: () {
        if (code != currentCode) {
          Get.updateLocale(Locale(code));
          Get.snackbar(
            'Language changed',
            'Current language: $title',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.black87,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
          );
        }
        Navigator.of(context, rootNavigator: true).pop();
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                fontFamily: 'ProximaNova',
                fontSize: 16.sp,
                color: Colors.white,
              ),
            ),
            if (code == currentCode)
              const Icon(Icons.check, color: Colors.white, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerSwitchTile(String title, type) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Obx(() => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontFamily: 'Proxima-Nova',
                fontSize: 14.sp,
                color: Color(0xFF515057),
              ),
            ),
          ),
          Transform.scale(
            scale: 0.001.sh,
            child: Switch(
              value: type == 'touchID' ? drawerCtrl.activeTouchID.value :
              type == 'saveSlip' ? drawerCtrl.saveSlip.value :
              type == 'onNotify' ? drawerCtrl.notify.value : false,
              onChanged: (value) {
                // setState(() {
                if (type == 'touchID') {
                  drawerCtrl.toggleTouchID();
                } else if (type == 'saveSlip') {
                  drawerCtrl.toggleSaveSlip();
                } else if (type == 'onNotify') {
                  drawerCtrl.toggleNotify();
                }
              },
              activeTrackColor: Color(0xff0FE8D8).withOpacity(0.8),
              inactiveThumbColor: Color(0xff929194),
              activeColor: Color(0xff0FE8D8),
              //                        inactiveThumbColor: Color(0xff929194),
              inactiveTrackColor: Color(0xff707070),
            ),
          ),
        ],
      ),
      ),
    );
  }

  Widget _buildDrawerTile(
      String title, {
        String? value,
        Widget? valueWidget,
        Widget? icon,
        VoidCallback? onTap,
      }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: icon,
      title: Text(
        title,
        style: TextStyle(
          fontFamily: 'ProximaNova',
          fontSize: 14.sp,
          color: Color(0xFF515057),
        ),
      ),
      trailing: valueWidget ??
          (value != null
              ? Text(
            value,
            style: TextStyle(
              fontFamily: 'ProximaNova',
              fontSize: 14.sp,
              color: Color(0xFF515057).withOpacity(0.7),
            ),
          )
              : null),
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    final selectedCurrencyKey = storage.read('selected_currency') ?? 'thb';
    final selectedCurrencyLabel = currencies.firstWhere(
            (c) => c['key'] == selectedCurrencyKey,
        orElse: () => currencies[1])['label'];
    print("🔥 Currency ปัจจุบันใน Drawer: $selectedCurrencyKey ($selectedCurrencyLabel)");

    return Drawer(
      backgroundColor: Color(0xff141322),
      child: SafeArea(
        child: Column(
          children: [
            Container(
              height: 60.h,
              width: double.infinity,
              padding: EdgeInsets.only(left: 16.w, top: 20.h),
              color: Color(0xff141322),
              child: Align(
                alignment: Alignment.topLeft,
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: SizedBox(
                    height: 15.h,
                    width: 20.w,
                    child: Image.asset(
                      LikeWalletImage.icon_menu,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                children: [
                  _buildDrawerTile(
                    'tabslide_language'.tr,
                    value: languages
                        .firstWhere((l) =>
                    l['code'] ==
                        (Get.locale?.languageCode ?? 'th'))['label']!
                        .tr,
                    onTap: () {
                      final newContext =
                          Navigator.of(context, rootNavigator: true).context;
                      Future.delayed(const Duration(milliseconds: 300), () {
                        dialogContent(newContext);
                      });
                    },
                  ),
                  _buildDrawerTile(
                    'tabslide_currency'.tr,
                    valueWidget: ValueListenableBuilder(
                      valueListenable: currencyNotifier,
                      builder: (context, value, _) {
                        final label = currencies.firstWhere(
                              (c) => c['key'] == value,
                          orElse: () => currencies[1],
                        )['label'];
                        return Text(
                          label!,
                          style: TextStyle(
                            fontFamily: 'ProximaNova',
                            fontSize: 14.sp,
                            color: Color(0xFF515057).withOpacity(0.7),
                          ),
                        );
                      },
                    ),
                    onTap: () {
                      final newContext =
                          Navigator.of(context, rootNavigator: true).context;
                      Future.delayed(const Duration(milliseconds: 300), () {
                        currencyDialog(newContext);
                      });
                    },
                  ),
                  _buildDrawerTile(
                    'Reward Currency',
                    valueWidget: GetBuilder<RewardCurrencyController>(
                      init: RewardCurrencyController(),
                      builder: (controller) {
                        return Text(
                          controller.getCurrentRewardCurrencyLabel(),
                          style: TextStyle(
                            fontFamily: 'ProximaNova',
                            fontSize: 14.sp,
                            color: Color(0xFF515057).withOpacity(0.7),
                          ),
                        );
                      },
                    ),
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const RewardCurrencyPage(),
                        ),
                      );
                    },
                  ),
                  _buildDrawerSwitchTile('tabslide_touch'.tr, 'touchID'),
                  _buildDrawerSwitchTile('tabslide_slip'.tr, 'saveSlip'),
                  _buildDrawerSwitchTile('Line Notify', 'onNotify'),
                  _buildDrawerTile("two_step_2fa_title".tr),
                  _buildDrawerTile("tabslide_profile".tr, onTap: () {
                    showPopup(context, (_) => ProfilePage());
                  }),
                  _buildDrawerTile("tabslide_kyc".tr),
                  _buildDrawerTile("tabslide_email".tr),
                  _buildDrawerTile("main_feedback".tr , onTap: () {
                    showPopup(context, (_) => FeedbackPage());
                  }),
                  _buildDrawerTile('MVP List', onTap: () {
                    final newContext =
                        Navigator.of(context, rootNavigator: true).context;
                    Future.delayed(const Duration(milliseconds: 300), () {
                      showMvpListDialog(newContext);
                    });
                  }),
                  _buildDrawerTile("tabslide_contact".tr, onTap: () {
                    Get.to(ContactUsPage());
                  }),
                  _buildDrawerTile(
                    "tabslide_logout".tr,
                    onTap: () {
                      Storage.logout();
                    },
                  ),
                  const Divider(color: Colors.white24),
                  _buildDrawerTile("tabslide_joincommunity".tr),
                  _buildDrawerTile('Facebook',
                      icon: Image.asset(
                        'assets/image/FB-Logo64.png',
                        width: 24.w,
                        height: 24.h,
                        fit: BoxFit.contain,
                      ), onTap: () {
                        _launchURL(url_facebook);
                      }),
                  _buildDrawerTile('Twitter',
                      icon: Image.asset(
                        'assets/image/twitter64.png',
                        width: 24.w,
                        height: 24.h,
                        fit: BoxFit.contain,
                      ), onTap: () {
                        _launchURL(url_twitter);
                      }),
                  _buildDrawerTile('Youtube',
                      icon: Image.asset(
                        'assets/image/youtube64.png',
                        width: 24.w,
                        height: 24.h,
                        fit: BoxFit.contain,
                      ), onTap: () {
                        _launchURL(url_youtube);
                      }),
                  const Divider(color: Colors.white24),
                  _buildDrawerTile('chat_operator'.tr),
                  _buildDrawerTile('tabslide_help'.tr, onTap: () {
                    Get.to(ContactUsPage());
                  }),
                  _buildDrawerTile('tabslide_share'.tr),
                  _buildDrawerTile('tabslide_privacy'.tr, onTap: () {
                    showPrivacyBottomSheet(context);
                  }),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("tabslide_version".tr,
                            style: TextStyle(
                                color: Color(0xFF515057).withOpacity(0.6))),
                        Text("tabslide_version_name".tr,
                            style: TextStyle(
                                color: Color(0xFF515057).withOpacity(0.6))),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 120.h),
          ],
        ),
      ),
    );
  }
}