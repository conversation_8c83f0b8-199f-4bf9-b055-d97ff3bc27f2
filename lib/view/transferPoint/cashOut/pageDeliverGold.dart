import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/view/like2crypto/popupTermPolicy.dart';
import 'package:likewallet/view/transferPoint/cashOut/slipConfirmation.dart';

class DeliverGold extends StatefulWidget {
  const DeliverGold({Key? key}) : super(key: key);

  @override
  State<DeliverGold> createState() => _DeliverGoldState();
}

class _DeliverGoldState extends State<DeliverGold> {

  ProfileController profileCtrl = Get.isRegistered<ProfileController>() ? Get.find<ProfileController>() : Get.put(ProfileController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
          body: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Color(0xFF2B2A38),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: mediaQuery(context, "height", 260),
                  width: MediaQuery.of(context).size.width * 0.88,
                  // alignment: Alignment.bottomCenter,
                  child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                    Image.asset(LikeWalletImage.icon_title_name,
                        height: 25.h),
                    SizedBox(width: 26.w),
                    Text(
                      "${profileCtrl.resProfile["firstName"]} ${profileCtrl.resProfile["lastName"]}",
                      style: TextStyle(
                        fontFamily: 'Proxima Nova',
                        fontSize: 13.sp,
                        color: const Color(0x4dffffff),
                        // letterSpacing: 1.17.,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ]),
                ),
                Container(
                    width: MediaQuery.of(context).size.width * 0.9,
                    height: MediaQuery.of(context).size.height * 0.85,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: SingleChildScrollView(
                      physics: NeverScrollableScrollPhysics(),
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.75,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              children: [
                                SizedBox(height: 24.h),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.8,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    'Deliver to',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontFamily: 'Proxima Nova',
                                      color: Color(0xFF8D8D8D),
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                                SizedBox(height: 24.h),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.8,
                                  height: MediaQuery.of(context).size.height * 0.07,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: Color(0xFF8D8D8D).withOpacity(0.5),
                                      width: 1,
                                    ),
                                  ),
                                  padding: EdgeInsets.only(left: 10),
                                  child: Center(
                                    child: TextField(
                                      decoration: InputDecoration(
                                        hintText: 'address',
                                        hintStyle: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFF1A1818).withOpacity(0.4),
                                          letterSpacing: 0.5,
                                        ),
                                        border: InputBorder.none,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 24.h),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.8,
                                  height: MediaQuery.of(context).size.height * 0.07,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: Color(0xFF8D8D8D).withOpacity(0.5),
                                      width: 1,
                                    ),
                                  ),
                                  padding: EdgeInsets.only(left: 10, right: 10),
                                  child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Select city',
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF1A1818),
                                          ),
                                        ),
                                        Icon(Icons.keyboard_arrow_down_rounded, color: Color(0xFF1A1818), size: 24,),
                                      ]
                                  ),
                                ),
                                SizedBox(height: 24.h),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.8,
                                  height: MediaQuery.of(context).size.height * 0.07,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: Color(0xFF8D8D8D).withOpacity(0.5),
                                      width: 1,
                                    ),
                                  ),
                                  padding: EdgeInsets.only(left: 10, right: 10),
                                  child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Select district',
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF1A1818),
                                          ),
                                        ),
                                        Icon(Icons.keyboard_arrow_down_rounded, color: Color(0xFF1A1818), size: 24,),
                                      ]
                                  ),
                                ),
                                SizedBox(height: 24.h),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.8,
                                  height: MediaQuery.of(context).size.height * 0.07,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: Color(0xFF8D8D8D).withOpacity(0.5),
                                      width: 1,
                                    ),
                                  ),
                                  padding: EdgeInsets.only(left: 10, right: 10),
                                  child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Select sub district',
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF1A1818),
                                          ),
                                        ),
                                        Icon(Icons.keyboard_arrow_down_rounded, color: Color(0xFF1A1818), size: 24,),
                                      ]
                                  ),
                                ),
                                SizedBox(height: 24.h),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.8,
                                  height: MediaQuery.of(context).size.height * 0.07,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: Color(0xFF8D8D8D).withOpacity(0.5),
                                      width: 1,
                                    ),
                                  ),
                                  padding: EdgeInsets.only(left: 10),
                                  child: Center(
                                    child: TextField(
                                      decoration: InputDecoration(
                                        hintText: "Recipient's fullname as stated on the ID card",
                                        hintStyle: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFF1A1818).withOpacity(0.4),
                                          letterSpacing: 0.1,
                                          fontWeight: FontWeight.w300,
                                        ),
                                        border: InputBorder.none,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 24.h),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.8,
                                  height: MediaQuery.of(context).size.height * 0.07,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: Color(0xFF8D8D8D).withOpacity(0.5),
                                      width: 1,
                                    ),
                                  ),
                                  padding: EdgeInsets.only(left: 10),
                                  child: Center(
                                    child: TextField(
                                      decoration: InputDecoration(
                                        hintText: "Recipient's phone number",
                                        hintStyle: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w300,
                                          color: Color(0xFF1A1818).withOpacity(0.4),
                                        ),
                                        border: InputBorder.none,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 24.h),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.75,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Processing takes 7–14 business days with EMS delivery and insurance up to THB 50,000. Fees apply.',
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF8D8D8D).withOpacity(0.5)),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          showDialog(
                                            context: context,
                                            useSafeArea: false,
                                            barrierColor: Colors.black.withOpacity(0.5),
                                            builder: (_) => PopupTermPolicy(),
                                          );
                                        },
                                        child: Container(
                                          height: 50.h,
                                          width: mediaQuery(context, 'width', 300),
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            'Read full terms',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                              fontFamily: 'Proxima Nova',
                                              color: Color(0xFF2FA2FA),
                                              decoration: TextDecoration.underline,),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: EdgeInsets.only(bottom: mediaQuery(context, 'height', 100)),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: <Widget>[
                                  GestureDetector(
                                    onTap: () {
                                      Navigator.pop(context);
                                    },
                                    child: Container(
                                        width: mediaQuery(context, 'width', 930) / 2,
                                        height: mediaQuery(context, 'height', 133),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          children: <Widget>[
                                            Image.asset(
                                              LikeWalletImage.icon_button_cancel_white,
                                              height: mediaQuery(context, 'height', 133),
                                            ),
                                            Text(
                                              'Back',
                                              style: TextStyle(
                                                  fontFamily: 'Proxima Nova',
                                                  fontSize:
                                                  mediaQuery(context, 'height', 36),
                                                  color: LikeWalletAppTheme.gray
                                                      .withOpacity(0.8)),
                                              textAlign: TextAlign.right,
                                            )
                                          ],
                                        )),
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => SlipComfirmation(
                                            firstName: profileCtrl.resProfile["firstName"],
                                            lastName: profileCtrl.resProfile["lastName"],
                                          ),
                                        ),
                                      );
                                    },
                                    child: Container(
                                        width: mediaQuery(context, 'width', 930) / 2,
                                        height: mediaQuery(context, 'height', 133),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          children: <Widget>[
                                            Text(
                                              'bankingbuy_button'.tr,
                                              style: TextStyle(
                                                  fontFamily: 'Proxima Nova',
                                                  fontSize:
                                                  mediaQuery(context, 'height', 36),
                                                  color: LikeWalletAppTheme.gray
                                                      .withOpacity(0.8)),
                                              textAlign: TextAlign.right,
                                            ),
                                            Image.asset(
                                              LikeWalletImage.icon_button_next_black,
                                              height: mediaQuery(context, 'height', 133),
                                            ),
                                          ],
                                        )),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                ),
              ],
            ),
          )
      ),
    );
  }
}