import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/cashOutController/cashOutController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/loading.dart';
import 'package:likewallet/view/like2crypto/faceAuthen.dart';
import 'package:likewallet/view/like2crypto/popupTermPolicy.dart';
import 'package:likewallet/view/transferPoint/cashOut/pageDeliverGold.dart';
import 'package:likewallet/view/transferPoint/cashOut/pageFiatCashOut.dart';
import 'package:likewallet/view/transferPoint/cashOut/slipConfirmation.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'dart:ui' as ui;

import '../../controller/rewardCurrency/rewardCurrencyController.dart';

class CashOutPage extends StatefulWidget {
  const CashOutPage({super.key});

  @override
  State<CashOutPage> createState() => _CashOutPageState();
}

class _CashOutPageState extends State<CashOutPage> {
  // Controller
  final CashOutController cashOutCtrl = Get.isRegistered<CashOutController>()
      ? Get.find<CashOutController>()
      : Get.put(CashOutController());
  final RewardCurrencyController rewardCurrencyCtrl = Get.isRegistered<RewardCurrencyController>()
      ? Get.find<RewardCurrencyController>()
      : Get.put(RewardCurrencyController());

  @override
  void initState() {
    super.initState();


    // SYNC asset ที่เลือกให้ตรงกับ reward currency
    WidgetsBinding.instance.addPostFrameCallback((_) {
      syncAssetWithRewardCurrency();
    });
  }

  void syncAssetWithRewardCurrency() {
    final key = rewardCurrencyCtrl.selectedRewardCurrency.value; // "like", "btc", "gold"
    if (key == 'like') {
      cashOutCtrl.changeAssetType(0); // LIKE
    } else if (key == 'btc') {
      cashOutCtrl.changeAssetType(1); // BTC
    } else if (key == 'gold') {
      cashOutCtrl.changeAssetType(2); // GOLD
    }
    setState(() {});
  }


  @override
  Widget build(BuildContext context) {
    return GetBuilder<CashOutController>(
      init: cashOutCtrl,
      builder: (controller) => ModalProgressHUD(
        inAsyncCall: controller.isLoading.value,
        opacity: 0.3,
        progressIndicator: const CustomLoading(),
        child: SingleChildScrollView(
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              Container(
                padding: EdgeInsets.only(
                  top: mediaQuery(context, 'height', 680),
                ),
                child: _buildBuyTab(),
              ),
              Positioned(
                bottom: 0.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    // Back button
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SizedBox(
                          width: mediaQuery(context, 'width', 930) / 2,
                          height: mediaQuery(context, 'height', 133),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: <Widget>[
                              Image.asset(
                                LikeWalletImage.icon_button_cancel_white,
                                height: mediaQuery(context, 'height', 133),
                              ),
                              Text(
                                'bankingbuy_back'.tr,
                                style: TextStyle(
                                    fontFamily: 'Noto Sans',
                                    fontSize:
                                    mediaQuery(context, 'height', 36),
                                    color: LikeWalletAppTheme.gray
                                        .withOpacity(0.8)),
                                textAlign: TextAlign.right,
                              )
                            ],
                          )),
                    ),
                    // Next button
                    GestureDetector(
                      onTap: () {
                        if(cashOutCtrl.selectPageCashOut.value == 2) {
                          // Gold delivery
                          Navigator.push(context, MaterialPageRoute(
                              builder: (context) => DeliverGold()));
                        } else if(cashOutCtrl.selectPageCashOut.value == 1) {
                          // BTC to crypto
                          // Navigator.push(context, MaterialPageRoute(
                          //     builder: (context) => const FaceAuthen()));
                          Navigator.push(context, MaterialPageRoute(
                              builder: (context) => const SlipComfirmation(
                                  fromCashOut: true,
                              )));
                        } else {
                          // LIKE to currency
                          // cashOutCtrl.proceedToNextStep(context);
                          Navigator.push(context, MaterialPageRoute(
                              builder: (context) => const BankCashOut()));
                        }
                      },
                      child: SizedBox(
                          width: mediaQuery(context, 'width', 930) / 2,
                          height: mediaQuery(context, 'height', 133),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: <Widget>[
                              Text(
                                'bankingbuy_button'.tr,
                                style: TextStyle(
                                    fontFamily: 'Noto Sans',
                                    fontSize:
                                    mediaQuery(context, 'height', 36),
                                    color: LikeWalletAppTheme.gray
                                        .withOpacity(0.8)),
                                textAlign: TextAlign.right,
                              ),
                              Image.asset(
                                LikeWalletImage.icon_button_next_black,
                                height: mediaQuery(context, 'height', 133),
                              ),
                            ],
                          )),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildBuyTab() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height * 0.62,
      alignment: Alignment.topCenter,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.56,
        decoration: BoxDecoration(
          color: LikeWalletAppTheme.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 5,
              blurRadius: 7,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.8,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 19.h),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.8,
                      height: MediaQuery.of(context).size.height * 0.042,
                      alignment: Alignment.centerLeft,
                      child: const Text(
                        'Select assets',
                        style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Proxima Nova',
                            color: Color(0xFF1A1818),
                            fontWeight: FontWeight.w700),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.8,
                      height: MediaQuery.of(context).size.height * 0.055,
                    ),
                    cashOutCtrl.selectPageCashOut.value == 0
                      ? _buildLikeCashOut()
                      : cashOutCtrl.selectPageCashOut.value == 1
                          ? _buildBTCCashOut()
                          : _buildGoldCashOut(),
                  ],
                ),
                Positioned(
                  top: (MediaQuery.of(context).size.height * 0.042) + (24.h),
                  child: GestureDetector(
                    onTap: () {
                      cashOutCtrl.toggleAssetSelect();
                    },
                    child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: MediaQuery.of(context).size.width * 0.8,
                        height: (MediaQuery.of(context).size.height * 0.06) *
                            (cashOutCtrl.assetSelect.value ? 3 : 1),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            color: const Color(0xFFFFFFFF),
                            border: Border.all(
                                width: mediaQuery(context, 'width', 1),
                                color: const Color(0xFF000000).withOpacity(0.2))),
                        alignment: Alignment.center,
                        child: SizedBox(
                            width: MediaQuery.of(context).size.width * 0.75,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                InkWell(
                                  onTap: () {
                                    if (!cashOutCtrl.assetSelect.value) {
                                      cashOutCtrl.toggleAssetSelect();
                                    } else {
                                      // Keep current selection
                                      cashOutCtrl.assetSelect.value = false;
                                    }
                                  },
                                  child: Container(
                                    child: Row(
                                      mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Image.asset(
                                              cashOutCtrl.selectPageCashOut.value == 0
                                                  ? 'assets/icon/icon_circle_black.png'
                                                  : cashOutCtrl.selectPageCashOut.value == 1
                                                  ? 'assets/icon/btc_icon.png'
                                                  : 'assets/icon/gold_icon.png',
                                              width: 30,
                                              height: 30,
                                            ),
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            cashOutCtrl.selectPageCashOut.value == 2 ?  const Text(
                                              'Gold',
                                              style: TextStyle(fontSize: 14,
                                                fontFamily: 'Proxima Nova',
                                                color: Color(0xFF1A1818),
                                                fontWeight: FontWeight.w600,
                                              ),
                                            )
                                                :Column(
                                              crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  cashOutCtrl.selectPageCashOut.value == 0
                                                      ? 'LIKE'
                                                      : cashOutCtrl.selectPageCashOut.value == 1
                                                      ? 'BTC'
                                                      : 'Gold',
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    fontFamily: 'Proxima Nova',
                                                    color: Color(0xFF1A1818),
                                                    fontWeight: FontWeight.w600,),
                                                ),
                                                Text(
                                                  cashOutCtrl.selectPageCashOut.value == 0
                                                      ? 'Likepoint'
                                                      : cashOutCtrl.selectPageCashOut.value == 1
                                                      ? 'Bitcoin'
                                                      : '',
                                                  style: TextStyle(
                                                    fontSize: 12.sp,
                                                    fontFamily: 'Proxima Nova',
                                                    color: const Color(0xAA1A1818),
                                                    fontWeight: FontWeight.w600,),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        RotationTransition(turns: AlwaysStoppedAnimation(
                                            cashOutCtrl.assetSelect.value ? 0.5 : 0),
                                          child: const Icon(Icons.keyboard_arrow_down_rounded),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                if (cashOutCtrl.assetSelect.value)
                                  InkWell(
                                    onTap: () {
                                      // Select second option
                                      if (cashOutCtrl.selectPageCashOut.value == 0) {
                                        cashOutCtrl.changeAssetType(1); // BTC
                                      } else if (cashOutCtrl.selectPageCashOut.value == 1) {
                                        cashOutCtrl.changeAssetType(2); // Gold
                                      } else {
                                        cashOutCtrl.changeAssetType(0); // LIKE
                                      }
                                    },
                                    child: Container(
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Row(
                                            children: [
                                              Image.asset(
                                                cashOutCtrl.selectPageCashOut.value == 0
                                                    ? 'assets/icon/btc_icon.png'
                                                    : cashOutCtrl.selectPageCashOut.value == 1
                                                    ? 'assets/icon/gold_icon.png'
                                                    : 'assets/icon/icon_circle_black.png',
                                                width: 30,
                                                height: 30,
                                              ),
                                              const SizedBox(
                                                width: 10,
                                              ),
                                              cashOutCtrl.selectPageCashOut.value == 1 ?  Text(
                                                'Gold',
                                                style: TextStyle(fontSize: 14.sp,
                                                  fontFamily: 'Proxima Nova',
                                                  color: const Color(0xFF1A1818),
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              )
                                                  : Column(
                                                crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    cashOutCtrl.selectPageCashOut.value == 0
                                                        ? 'BTC'
                                                        : cashOutCtrl.selectPageCashOut.value == 1
                                                        ? 'Gold'
                                                        : 'LIKE',
                                                    style: TextStyle(
                                                      fontSize: 14.sp,
                                                      fontFamily: 'Proxima Nova',
                                                      color:
                                                      const Color(0xFF1A1818),
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                  Text(
                                                    cashOutCtrl.selectPageCashOut.value == 0
                                                        ? 'Bitcoin'
                                                        : cashOutCtrl.selectPageCashOut.value == 1
                                                        ? ''
                                                        : 'Likepoint',
                                                    style: TextStyle(
                                                      fontSize: 12.sp,
                                                      fontFamily: 'Proxima Nova',
                                                      color:
                                                      const Color(0xAA1A1818),
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                          Container()
                                        ],
                                      ),
                                    ),
                                  ),
                                if (cashOutCtrl.assetSelect.value)
                                  InkWell(
                                    onTap: () {
                                      // Select third option
                                      if (cashOutCtrl.selectPageCashOut.value == 0) {
                                        cashOutCtrl.changeAssetType(2); // Gold
                                      } else if (cashOutCtrl.selectPageCashOut.value == 1) {
                                        cashOutCtrl.changeAssetType(0); // LIKE
                                      } else {
                                        cashOutCtrl.changeAssetType(1); // BTC
                                      }
                                    },
                                    child: Container(
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Row(
                                            children: [
                                              Image.asset(
                                                cashOutCtrl.selectPageCashOut.value == 0
                                                    ? 'assets/icon/gold_icon.png'
                                                    : cashOutCtrl.selectPageCashOut.value == 1
                                                    ? 'assets/icon/icon_circle_black.png'
                                                    : 'assets/icon/btc_icon.png',
                                                width: 30,
                                                height: 30,
                                              ),
                                              const SizedBox(
                                                width: 10,
                                              ),
                                              cashOutCtrl.selectPageCashOut.value == 0
                                                  ? Text(
                                                'Gold',
                                                style: TextStyle(
                                                  fontSize: 14.sp,
                                                  fontFamily: 'Proxima Nova',
                                                  color: const Color(
                                                      0xFF1A1818),
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              )
                                                  : Column(
                                                crossAxisAlignment:
                                                CrossAxisAlignment
                                                    .start,
                                                children: [
                                                  Text(
                                                    cashOutCtrl.selectPageCashOut.value == 0
                                                        ? 'Gold'
                                                        : cashOutCtrl.selectPageCashOut.value ==
                                                        1
                                                        ? 'LIKE'
                                                        : 'BTC',
                                                    style: TextStyle(
                                                      fontSize: 14.sp,
                                                      fontFamily: 'Proxima Nova',
                                                      color: const Color(
                                                          0xFF1A1818),
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                  Text(
                                                    cashOutCtrl.selectPageCashOut.value == 1
                                                        ? 'Likepoint'
                                                        : 'Bitcoin',
                                                    style: TextStyle(
                                                      fontSize: 12.sp,
                                                      fontFamily: 'Proxima Nova',
                                                      color: const Color(
                                                          0xAA1A1818),
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                          Container()
                                        ],
                                      ),
                                    ),
                                  ),
                              ],
                            ))),
                    ),
                  ),
                Positioned(
                  top: (MediaQuery.of(context).size.height * 0.234) + (60.h),
                  child: cashOutCtrl.selectPageCashOut.value == 0 ?  GestureDetector(
                    onTap: () {
                      cashOutCtrl.toggleCurrencySelect();
                    },
                    child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: MediaQuery.of(context).size.width * 0.8,
                        height: (MediaQuery.of(context).size.height * 0.06) *
                            (cashOutCtrl.currencySelect.value ? 3 : 1),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            color: const Color(0xFFFFFFFF),
                            border: Border.all(
                                width: mediaQuery(context, 'width', 1),
                                color: const Color(0xFF000000).withOpacity(0.2))),
                        alignment: Alignment.center,
                        child: SizedBox(
                            width: MediaQuery.of(context).size.width * 0.75,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    if (!cashOutCtrl.currencySelect.value) {
                                      cashOutCtrl.toggleCurrencySelect();
                                    } else {
                                      // Keep current selection
                                      cashOutCtrl.currencySelect.value = false;
                                    }
                                  },
                                  child: Container(
                                    child: Row(
                                      mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          '${cashOutCtrl.currencies[cashOutCtrl.selectCurrency.value]} - ${cashOutCtrl.currencyNames[cashOutCtrl.selectCurrency.value]}',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontFamily: 'Proxima Nova',
                                            color: Color(0xFF1A1818),
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                        RotationTransition(turns: AlwaysStoppedAnimation(
                                            cashOutCtrl.currencySelect.value ? 0.5 : 0),
                                          child: const Icon(Icons.keyboard_arrow_down_rounded),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                if (cashOutCtrl.currencySelect.value)
                                  GestureDetector(
                                    onTap: () {
                                      // Select second option
                                      int nextIndex = (cashOutCtrl.selectCurrency.value + 1) % 3;
                                      cashOutCtrl.changeCurrency(nextIndex);
                                    },
                                    child: Container(
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            '${cashOutCtrl.currencies[(cashOutCtrl.selectCurrency.value + 1) % 3]} - ${cashOutCtrl.currencyNames[(cashOutCtrl.selectCurrency.value + 1) % 3]}',
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontFamily: 'Proxima Nova',
                                              color: Color(0xFF1A1818),
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                          Container()
                                        ],
                                      ),
                                    ),
                                  ),
                                if (cashOutCtrl.currencySelect.value)
                                  GestureDetector(
                                    onTap: () {
                                      // Select third option
                                      int nextIndex = (cashOutCtrl.selectCurrency.value + 2) % 3;
                                      cashOutCtrl.changeCurrency(nextIndex);
                                    },
                                    child: Container(
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            '${cashOutCtrl.currencies[(cashOutCtrl.selectCurrency.value + 2) % 3]} - ${cashOutCtrl.currencyNames[(cashOutCtrl.selectCurrency.value + 2) % 3]}',
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontFamily: 'Proxima Nova',
                                              color: Color(0xFF1A1818),
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                          Container()
                                        ],
                                      ),
                                    ),
                                  ),
                              ],
                            ))),
                    ) : Container(),
                  ) ,
                cashOutCtrl.selectPageCashOut.value == 1 ? Positioned(
                  top: (MediaQuery.of(context).size.height * 0.194) + (80.h),
                  child: GestureDetector(
                    onTap: () {
                      cashOutCtrl.toggleCurrencySelect();
                    },
                    child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: MediaQuery.of(context).size.width * 0.8,
                        height: (MediaQuery.of(context).size.height * 0.06) *
                            (cashOutCtrl.currencySelect.value ? 1 : 1),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            color: const Color(0xFFFFFFFF),
                            border: Border.all(
                                width: mediaQuery(context, 'width', 1),
                                color: const Color(0xFF000000).withOpacity(0.2))),
                        alignment: Alignment.center,
                        child: SizedBox(
                            width: MediaQuery.of(context).size.width * 0.75,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      'BTC-mainnet',
                                      style: TextStyle(
                                          fontSize: 14,
                                          fontFamily: 'Proxima Nova',
                                          fontWeight: FontWeight.w700,
                                          color: Color(0xFF1A1818)),
                                    ),
                                    RotationTransition(turns: AlwaysStoppedAnimation(
                                        cashOutCtrl.currencySelect.value ? 0.5 : 0),
                                      child: const Icon(Icons.keyboard_arrow_down_rounded),
                                    ),
                                  ],
                                ),
                                // if (cashOutCtrl.currencySelect.value)
                                //   Row(
                                //     mainAxisAlignment:
                                //     MainAxisAlignment.spaceBetween,
                                //     children: [
                                //       const Text(
                                //         'BCT-mainnet',
                                //         style: TextStyle(
                                //             fontSize: 14,
                                //             fontFamily: 'Proxima Nova',
                                //             fontWeight: FontWeight.w700,
                                //             color: Color(0xFF1A1818)),
                                //       ),
                                //       Container()
                                //     ],
                                //   ),
                                // if (cashOutCtrl.currencySelect.value)
                                //   Row(
                                //     mainAxisAlignment:
                                //     MainAxisAlignment.spaceBetween,
                                //     children: [
                                //       const Text(
                                //         'BCT-mainnet',
                                //         style: TextStyle(
                                //             fontSize: 14,
                                //             fontFamily: 'Proxima Nova',
                                //             fontWeight: FontWeight.w700,
                                //             color: Color(0xFF1A1818)),
                                //       ),
                                //       Container()
                                //     ],
                                //   ),
                              ],
                            ))),
                    ),
                  ) : Container(),
              ],
            )),
      ),
    );
  }

  Widget _buildLikeCashOut() {
    return Column(
      children: [
        SizedBox(height: 16.h),
        Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.1,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                    width: mediaQuery(context, 'width', 1),
                    color: const Color(0xFF000000).withOpacity(0.2))),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: MediaQuery.of(context).size.width * 0.75,
                  height: MediaQuery.of(context).size.height * 0.045,
                  alignment: Alignment.bottomLeft,
                  child: GestureDetector(
                    onTap: () {
                      // Set max amount
                      // TODO: Implement max amount logic
                    },
                    child: Container(
                      width: 45,
                      height: 26,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border:
                          Border.all(color: const Color(0xFFB8D54C))),
                      alignment: Alignment.center,
                      child: const Text(
                        'Max',
                        style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Proxima Nova',
                            color: Color(0xFFB8D54C)),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                    width: MediaQuery.of(context).size.width * 0.75,
                    child: Stack(
                      children: [
                        Positioned(
                          bottom: 0,
                          child: SizedBox(
                            width:
                            MediaQuery.of(context).size.width *
                                0.75,
                            child: Divider(
                              color: const Color(0xFF1A181814)
                                  .withOpacity(0.1),
                              thickness: 1,
                            ),
                          ),
                        ),
                        cashOutCtrl.amountController.text.isEmpty
                          ? Positioned(
                            left: 0,
                            bottom:
                            MediaQuery.of(context).size.height *
                                0.012,
                            child: const Text(
                              "Pass amount",
                              style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'Proxima Nova',
                                  fontWeight: FontWeight.w300,
                                  color: Color(0xFFBABABA)),
                            ),
                          ) : Container(),
                        SizedBox(
                          width: MediaQuery.of(context).size.width *
                              0.75,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              SizedBox(
                                width: (MediaQuery.of(context).size.width *
                                    0.75) - ((48.h) + 26),
                                height: MediaQuery.of(context).size.height *
                                    0.05,
                                child: TextField(
                                  controller: cashOutCtrl.amountController,
                                  textAlign: TextAlign.end,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    color: Color(0xFF8C8B8B),
                                    fontFamily: 'Proxima Nova',
                                    fontWeight: FontWeight.w400,
                                  ),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    hintText: '0.00',
                                    hintStyle: TextStyle(
                                      fontSize: 24,
                                      color: Color(0xFF8C8B8B),
                                      fontFamily: 'Proxima Nova',
                                      fontWeight: FontWeight.w400,
                                    ),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 5),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    cashOutCtrl.updateAmount();
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                height: 24.h,
                                alignment: Alignment.bottomCenter,
                                child: const Text(
                                  'LIKE',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'Proxima Nova',
                                    color: Color(0xFF8D8D8D),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ))
              ],
            )),
        SizedBox(height: 12.h),
        Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.042,
          alignment: Alignment.centerLeft,
          child: const Text(
            'Select currency',
            style: TextStyle(
                fontSize: 14,
                fontFamily: 'Proxima Nova',
                color: Color(0xFF1A1818),
                fontWeight: FontWeight.w700),
          ),
        ),
        SizedBox(height: 8.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.06,
        ),
        Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.1,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: const Color(0xFFFFFFFF),
                border: Border.all(
                    width: mediaQuery(context, 'width', 1),
                    color: const Color(0xFF000000).withOpacity(0.2))),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 45,
                  height: 26,
                ),
                SizedBox(
                    width: MediaQuery.of(context).size.width * 0.75,
                    child: Stack(
                      children: [
                        Positioned(
                          bottom: 0,
                          child: SizedBox(
                            width:
                            MediaQuery.of(context).size.width *
                                0.75,
                            child: Divider(
                              color: const Color(0xFF1A181814)
                                  .withOpacity(0.1),
                              thickness: 1,
                            ),
                          ),
                        ),
                        cashOutCtrl.amountController.text.isEmpty
                          ? Positioned(
                            left: 0,
                            bottom: MediaQuery.of(context).size.height *
                                0.012,
                            child: const Text(
                              "Pass amount",
                              style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'Proxima Nova',
                                  fontWeight: FontWeight.w300,
                                  color: Color(0xFFBABABA)),
                            ),
                          ) : Container(),
                        SizedBox(
                          width: MediaQuery.of(context).size.width *
                              0.75,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              SizedBox(
                                width: (MediaQuery.of(context).size.width *
                                    0.75) - ((48.h) + 26),
                                height: MediaQuery.of(context).size.height *
                                    0.05,
                                child: TextField(
                                  controller: cashOutCtrl.bahtController,
                                  textAlign: TextAlign.end,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    color: Color(0xFF8C8B8B),
                                    fontFamily: 'Proxima Nova',
                                    fontWeight: FontWeight.w400,
                                  ),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    hintText: '0.00',
                                    hintStyle: TextStyle(
                                      fontSize: 24,
                                      color: Color(0xFF8C8B8B),
                                      fontFamily: 'Proxima Nova',
                                      fontWeight: FontWeight.w400,
                                    ),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 5),
                                  ),
                                  keyboardType:
                                  TextInputType.number,
                                  onChanged: (value) {
                                    cashOutCtrl.updateBaht();
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                height: 24.h,
                                alignment: Alignment.bottomCenter,
                                child: Text(
                                  cashOutCtrl.currencies[cashOutCtrl.selectCurrency.value],
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'Proxima Nova',
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF8D8D8D),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ))
              ],
            )),
        SizedBox(height: 16.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('FEE',
                  style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
              Row(
                children: [
                  Text(cashOutCtrl.fee.value.toStringAsFixed(2),
                      style: const TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                  Text(' ${cashOutCtrl.currencies[cashOutCtrl.selectCurrency.value]}',
                      style: const TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                ],
              )
            ],
          ),
        ),
        SizedBox(height: 8.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('You will receive',
                  style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
              Row(
                children: [
                  Text((cashOutCtrl.amount.value - cashOutCtrl.fee.value).toStringAsFixed(2),
                      style: const TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                  Text(' ${cashOutCtrl.currencies[cashOutCtrl.selectCurrency.value]}',
                      style: const TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBTCCashOut() {
    return Column(
      children: [
        SizedBox(height: 16.h),
        Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.06,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              // color: Color(0xFFFFFFFF),
              // color: Colors.red.withOpacity(0.5),
              border: Border.all(
                  width: mediaQuery(context, 'width', 1),
                  color: const Color(0xFF000000).withOpacity(0.2))),
          alignment: Alignment.center,
          // child: Container(
          //   width: MediaQuery.of(context).size.width * 0.75,
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       Text("Pass address",
          //         style: TextStyle(
          //             fontSize: 14,
          //             fontFamily: 'Proxima Nova',
          //             fontWeight: FontWeight.w300,
          //             color: Color(0xFFBABABA)),
          //       ),
          //       Image.asset('assets/image/like2crypto/lucide_scan.png', width: 64.h, height: 64.h, color: Color(0xFF1A1818),)
          //     ],
          //   ),
          // )
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.75,
            child: Stack(
              alignment: Alignment.center,
              children: [
                if (cashOutCtrl.addressController.text == '')
                  const Positioned(
                    left: 0,
                    child: Text(
                      "Pass address",
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',
                          fontWeight: FontWeight.w300,
                          color: Color(0xFFBABABA)),
                    ),
                  ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: (MediaQuery.of(context).size.width *
                          0.75) - ((48.h) + 26),
                      height: MediaQuery.of(context).size.height *
                          0.05,
                      // alignment: Alignment.center,
                      // color: Colors.red.withOpacity(0.5),
                      child: TextField(
                        controller: cashOutCtrl.addressController,
                        textAlign: TextAlign.start,
                        // ทำให้ข้อความที่ป้อนและ hint ชิดขวา
                        style: const TextStyle(
                          fontSize: 20,
                          color: Color(0xFF8C8B8B),
                          fontFamily: 'Proxima Nova',
                          fontWeight: FontWeight.w400,
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          hintStyle: TextStyle(
                            fontSize: 20,
                            color: Color(0xFF8C8B8B),
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.w400,
                          ),

                          contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                        ),
                        keyboardType: TextInputType.text,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')),
                        ],
                        onChanged: (value) {
                          setState(() {});
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 64.h,
                      height: 64.h,
                    )
                  ],
                ),
                Positioned(
                  right: 0,
                  child: Image.asset(
                    'assets/image/like2crypto/lucide_scan.png',
                    width: 32.h,
                    height: 32.h,
                    color: const Color(0xFF1A1818),
                  ),
                )
              ],
            ),
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.042,
          alignment: Alignment.centerLeft,
          child: const Text(
            'Select currency',
            style: TextStyle(
                fontSize: 14,
                fontFamily: 'Proxima Nova',
                color: Color(0xFF1A1818),
                fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(height: 16.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.06,
        ),
        SizedBox(height: 8.h),
        Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.1,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                // color: Color(0xFFFFFFFF),
                // color: Colors.red.withOpacity(0.5),
                border: Border.all(
                    width: mediaQuery(context, 'width', 1),
                    color: const Color(0xFF000000).withOpacity(0.2))),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: MediaQuery.of(context).size.width * 0.75,
                  height: MediaQuery.of(context).size.height * 0.045,
                  alignment: Alignment.bottomLeft,
                  child: Container(
                    width: 45,
                    height: 26,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border:
                        Border.all(color: const Color(0xFFB8D54C))),
                    alignment: Alignment.center,
                    child: const Text(
                      'Max',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',
                          color: Color(0xFFB8D54C)),
                    ),
                  ),
                ),
                SizedBox(
                    width: MediaQuery.of(context).size.width * 0.75,
                    // height: MediaQuery.of(context).size.height * 0.05,
                    // color: Colors.red.withOpacity(0.5),
                    child: Stack(
                      children: [
                        Positioned(
                          bottom: 0,
                          child: SizedBox(
                            width:
                            MediaQuery.of(context).size.width *
                                0.75,
                            child: Divider(
                              color: const Color(0xFF1A181814)
                                  .withOpacity(0.1),
                              thickness: 1,
                            ),
                          ),
                        ),
                        if (cashOutCtrl.amountController.text == '')
                          Positioned(
                            left: 0,
                            bottom:
                            MediaQuery.of(context).size.height *
                                0.012,
                            child: const Text(
                              "Pass amount",
                              style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'Proxima Nova',
                                  fontWeight: FontWeight.w300,
                                  color: Color(0xFFBABABA)),
                            ),
                          ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width *
                              0.75,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              SizedBox(
                                width: (MediaQuery.of(context).size.width *
                                    0.75) - ((48.h) + 26),
                                height: MediaQuery.of(context).size.height *
                                    0.05,
                                child: TextField(
                                  controller: cashOutCtrl.btcController,
                                  textAlign: TextAlign.end,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    color: Color(0xFF8C8B8B),
                                    fontFamily: 'Proxima Nova',
                                    fontWeight: FontWeight.w400,
                                  ),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    hintText: '0.00',
                                    hintStyle: TextStyle(
                                      fontSize: 24,
                                      color: Color(0xFF8C8B8B),
                                      fontFamily: 'Proxima Nova',
                                      fontWeight: FontWeight.w400,
                                    ),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 5),
                                  ),
                                  keyboardType:
                                  TextInputType.number,
                                  onChanged: (value) {
                                    cashOutCtrl.updateBTC();
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                height: 24.h,
                                alignment: Alignment.bottomCenter,
                                child: Text(
                                  'BTC',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'Proxima Nova',
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF8D8D8D),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // SizedBox(
                        //   width: MediaQuery.of(context).size.width *
                        //       0.75,
                        //   // color: Colors.red.withOpacity(0.5),
                        //   child: Row(
                        //     mainAxisAlignment: MainAxisAlignment.end,
                        //     children: [
                        //       Container(
                        //         // color: Colors.red.withOpacity(0.5),
                        //         height: 30.h,
                        //         alignment: Alignment.topCenter,
                        //         child: const Text(
                        //           'BTC',
                        //           style: TextStyle(
                        //             fontSize: 14,
                        //             fontFamily: 'Proxima Nova',
                        //             color: Color(0xFF8D8D8D),
                        //             fontWeight: FontWeight.w400,
                        //           ),
                        //         ),
                        //       ), // คำว่า LIKE จะอยู่ถาวร
                        //     ],
                        //   ),
                        // ),
                      ],
                    ))
              ],
            )),
        SizedBox(height: 16.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('FEE',
                  style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
              Row(
                children: [
                  Text('0.0010',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                  Text(' BTC',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                ],
              )
            ],
          ),
        ),
        SizedBox(height: 8.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('You will receive',
                  style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
              Row(
                children: [
                  Text('${cashOutCtrl.btc.value}',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                  Text(' BTC',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGoldCashOut() {
    return Column(
      children: [
        SizedBox(height: 18.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          child: Wrap(
            spacing: 8.0, // ระยะห่างระหว่าง Container ในแนวนอน
            runSpacing: 8.0, // ระยะห่างระหว่างแถวเมื่อขึ้นบรรทัดใหม่
            alignment: WrapAlignment.start,
            children: [
              InkWell(
                onTap: () {
                  cashOutCtrl.selectGoldWeight.value = 0;
                  cashOutCtrl.amountGoldController.text = "1.00";

                  setState(() {});
                },
                child: CustomContainer(
                  "1 g",
                  cashOutCtrl.selectGoldWeight.value == 0 ? const Color(0xFFF1B32B):Colors.white,
                ),
              ),
              InkWell(
                onTap: () {
                  cashOutCtrl.selectGoldWeight.value = 1;
                  cashOutCtrl.amountGoldController.text = "3.811";

                  setState(() {});
                },
                child: CustomContainer(
                  "3.811 g",
                  cashOutCtrl.selectGoldWeight.value == 1 ? const Color(0xFFF1B32B):Colors.white,
                ),
              ),
              InkWell(
                onTap: () {
                  cashOutCtrl.selectGoldWeight.value = 2;
                  cashOutCtrl.amountGoldController.text = "7.622";

                  setState(() {});
                },
                child: CustomContainer(
                  "7.622 g",
                  cashOutCtrl.selectGoldWeight.value == 2 ? const Color(0xFFF1B32B):Colors.white,
                ),
              ),
              InkWell(
                onTap: () {
                  cashOutCtrl.selectGoldWeight.value = 3;
                  cashOutCtrl.amountGoldController.text = "11.433";

                  setState(() {});
                },
                child: CustomContainer(
                  "11.433 g",
                  cashOutCtrl.selectGoldWeight.value == 3 ? const Color(0xFFF1B32B):Colors.white,
                ),
              ),
              InkWell(
                onTap: () {
                  cashOutCtrl.selectGoldWeight.value = 4;
                  cashOutCtrl.amountGoldController.text = "15.244";

                  setState(() {});
                },
                child: CustomContainer(
                  "15.244 g",
                  cashOutCtrl.selectGoldWeight.value == 4 ? const Color(0xFFF1B32B):Colors.white,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.h),
        Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.1,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                // color: Color(0xFFFFFFFF),
                // color: Colors.red.withOpacity(0.5),
                border: Border.all(
                    width: mediaQuery(context, 'width', 1),
                    color: const Color(0xFF000000).withOpacity(0.2))),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: MediaQuery.of(context).size.width * 0.73,
                  // color: Colors.red.withOpacity(0.5),
                  alignment: Alignment.centerLeft,
                  child: Container(
                    height: 30.h,
                    alignment: Alignment.bottomLeft,
                    child: const Text(
                      'Paid it all',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'Proxima Nova',
                        color: Color(0xFF8D8D8D),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width *
                      0.75,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SizedBox(
                        width: (MediaQuery.of(context).size.width *
                            0.75) - ((48.h) + 48),
                        height: MediaQuery.of(context).size.height *
                            0.05,
                        // alignment: Alignment.centerRight,
                        // color: Colors.red.withOpacity(0.5),
                        child: TextField(
                          controller: cashOutCtrl.amountGoldController,
                          readOnly: true,
                          textAlign: TextAlign.end,
                          // ทำให้ข้อความที่ป้อนและ hint ชิดขวา
                          style: const TextStyle(
                            fontSize: 20,
                            color: Color(0xFF8C8B8B),
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.w400,
                          ),
                          decoration: const InputDecoration(
                            // isDense: true,
                            border: InputBorder.none,
                            hintText: '0.00',
                            hintStyle: TextStyle(
                              fontSize: 20,
                              color: Color(0xFF8C8B8B),
                              fontFamily: 'Proxima Nova',
                              fontWeight: FontWeight.w400,
                            ),
                            contentPadding: EdgeInsets.zero,
                          ),
                          keyboardType:
                          TextInputType.number,
                          onChanged: (value) {
                            setState(() {});
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        height: 20.h,
                        alignment: Alignment.bottomCenter,
                        child: const Text(
                          'G (gram)',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Proxima Nova',
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF8D8D8D),
                          ),
                        ),
                      ), // คำว่า LIKE จะอยู่ถาวร
                    ],
                  ),
                ),
              ],
            )),
        SizedBox(height: 18.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Will be duducted.',
                  style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
              Row(
                children: [
                  Text('0.00',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                  Text(' G (gram)',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                ],
              )
            ],
          ),
        ),
        SizedBox(height: 8.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('You will receive',
                  style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
              Row(
                children: [
                  Text('0.00',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                  Text(' G (gram)',
                      style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Proxima Nova',color: Color(0xFF8D8D8D))),
                ],
              )
            ],
          ),
        ),
        SizedBox(height: 10.h),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.75,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Processing takes 7–14 business days with EMS delivery and insurance up to THB 50,000. Fees apply.',
                style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'Proxima Nova',color: const Color(0xFF8D8D8D).withOpacity(0.5)),
              ),
              GestureDetector(
                onTap: () {
                  showDialog(
                    context: context,
                    useSafeArea: false,
                    barrierColor: Colors.black.withOpacity(0.5),
                    builder: (_) => const PopupTermPolicy(),
                  );
                },
                child: Container(
                  height: 32.h,
                  width: mediaQuery(context, 'width', 300),
                  alignment: Alignment.topLeft,
                  // color: Colors.red.withOpacity(0.5),
                  child: const Text(
                    'Read full terms',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'Proxima Nova',
                      color: Color(0xFF2FA2FA),
                      decoration: TextDecoration.underline,),
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget CustomContainer(text, color) {
    // คำนวณความกว้างของ Text โดยใช้ TextPainter
    var textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: const TextStyle(fontSize: 16, color: Colors.white),
      ),
      maxLines: 1,
      textDirection: ui.TextDirection.ltr,
    )..layout();

    // ความกว้างของ Text + MediaQuery.of(context).size.width * 0.1
    final textWidth = textPainter.width;
    final containerWidth = textWidth + MediaQuery.of(context).size.width * 0.125;
    final textHeight = textPainter.height;
    final containerHeight = textHeight + MediaQuery.of(context).size.height * 0.02;

    return Container(
      width: containerWidth, // ความกว้างของ Container = ความกว้างของ Text + 10% ของหน้าจอ
      height: containerHeight,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        border: color == Colors.white ?Border.all(color: const Color(0xFFEDEDED)) : null,
      ),
      child: Center(
        child: Text(
          text,
          style: const TextStyle(color: Colors.black,
            fontFamily: 'Proxima Nova',),
        ),
      ),
    );
  }
}
