import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/service/components.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/loading.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:permission_handler/permission_handler.dart';

class ConfirmTransection extends StatefulWidget {
  const ConfirmTransection({super.key});

  @override
  State<ConfirmTransection> createState() => _ConfirmTransectionState();
}

class _ConfirmTransectionState extends State<ConfirmTransection> {
  bool _saving = false;
  bool selected = false;
  final FocusNode myFocusNode = FocusNode();

  // Get the transfer controller
  final TransferController transferCtrl = Get.find<TransferController>();

  @override
  void initState() {
    super.initState();
    // Use the controller's note controller
    myFocusNode.addListener(() {
      if (!myFocusNode.hasFocus) {
        setState(() {
          selected = false;
        });
      }
    });

    // Prepare transaction data for display
    transferCtrl.prepareTransactionData();
  }

  // Method to handle refresh action
  Future<void> _refreshData() async {
    // Show loading indicator during refresh
    await EasyLoading.show(
      status: 'refreshing_data'.tr,
      maskType: EasyLoadingMaskType.black,
      dismissOnTap: false,
    );

    try {
      // Reload transaction data
      transferCtrl.prepareTransactionData();

      // Dismiss loading indicator
      await EasyLoading.dismiss();

      // Optional: Show success message (brief duration)
      Get.snackbar(
        'Success',
        'Transaction data refreshed',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(milliseconds: 800),
      );
    } catch (e) {
      print('Error refreshing data: $e');

      // Dismiss loading indicator
      await EasyLoading.dismiss();

      // Show error message
      Get.snackbar(
        'Error',
        'Failed to refresh data',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _sendTransaction() async {
    setState(() {
      _saving = true;
    });

    try {
      // Use the controller to send the transaction
      await EasyLoading.show(
        status: 'loading_send_text1'.tr,
        maskType: EasyLoadingMaskType.black,
        dismissOnTap: false,
      );

      bool success = await transferCtrl.sendTransaction();

      EasyLoading.dismiss();

      if (success) {
        Get.back();
        Get.snackbar(
          'Success',
          'Transaction completed successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        // Get.snackbar(
        //   'Error',
        //   'Transaction failed',
        //   snackPosition: SnackPosition.BOTTOM,
        //   backgroundColor: Colors.red,
        //   colorText: Colors.white,
        // );
      }
    } catch (e) {
      EasyLoading.dismiss();
      print(e);
      Get.snackbar(
        'Error',
        'Transaction failed: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _saving = false;
      });
    }
  }

  Widget backButtonLemon(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Container(
          height: 0.05.sh,
          width: 0.19.sw,
          decoration: BoxDecoration(
            color: const Color(0xffB4E60D),
            borderRadius: const BorderRadius.only(
                bottomRight: Radius.circular(40.0),
                topRight: Radius.circular(40.0)),
            boxShadow: [
              BoxShadow(
                spreadRadius: 0,
                blurRadius: 9,
                color: const Color(0xff707071).withOpacity(0.1),
                offset: const Offset(
                  0.0,
                  3.0,
                ),
              ),
            ],
          ),
          alignment: Alignment.centerLeft,
          // margin: EdgeInsets.only(left: ),
          padding: EdgeInsets.symmetric(
              vertical: mediaQuery(context, 'height', 32.2),
              horizontal: mediaQuery(context, 'width', 71.3)),
          child: Image.asset(
            height: 0.1.sh,
            width: 0.2.sw,
            LikeWalletImage.icon_back_button,
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for better responsiveness
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: ModalProgressHUD(
        opacity: 0.1,
        inAsyncCall: _saving,
        progressIndicator: const CustomLoading(),
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Stack(
            children: <Widget>[
              bg(),
              // Back button positioning
              Positioned(
                top: 80.h, // Adjusted for better positioning
                left: 0,
                child: backButtonLemon(context),
              ),
              // Confirm button positioning
              Positioned(
                bottom: screenHeight * 0.25, // Position from bottom for better visibility
                right: screenWidth * 0.1, // Position from right
                child: GestureDetector(
                  onTap: () async {
                    try {
                      print("click");

                      // var showmeParam = {
                      //   "txHash": "0x35af1ac245e9e6c072eb2e247b6d8d50c942e1965b96ba47f693dc4a77975785",
                      //   "recipient": "0x5bdd4b007972fc1726798298138455f59ca78978",
                      //   "recipientName": "0x5bdd4b007972fc1726798298138455f59ca78978",
                      //   "amount": 1,
                      //   "isVendingTx": false
                      // };
                      //
                      // await transferCtrl.showTransactionSlip(
                      //   txHash: "0x35af1ac245e9e6c072eb2e247b6d8d50c942e1965b96ba47f693dc4a77975785",
                      //   recipient: "0x5bdd4b007972fc1726798298138455f59ca78978",
                      //   recipientName: "0x5bdd4b007972fc1726798298138455f59ca78978",
                      //   amount: "1",
                      //   isVendingTx: false,
                      // );
                      // return ;
                      var permissionStatus;

                      if (Platform.isAndroid) {
                        // ขอ permission สำหรับ Android
                        var status = await Permission.storage.status;
                        print("Android: check status $status");

                        if (status.isDenied || status.isRestricted || status.isPermanentlyDenied) {
                          permissionStatus = await Permission.storage.request();
                        } else {
                          permissionStatus = status;
                        }

                      } else if (Platform.isIOS) {
                        // ขอ permission สำหรับ iOS
                        var status = await Permission.photos.status;
                        print("iOS: check status $status");

                        if (status.isDenied || status.isRestricted || status.isPermanentlyDenied) {
                          print("go here");
                          permissionStatus = await Permission.photos.request();
                        } else {
                          permissionStatus = status;
                        }
                      }

                      // เช็กผลลัพธ์
                      if (permissionStatus == PermissionStatus.granted) {
                        _sendTransaction();
                      } else {
                        Get.snackbar('alert_permission_store_head'.tr,
                            'alert_permission_store_detail'.tr);
                      }

                    } catch (e) {
                      print("Error: $e");
                    }
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 95.r, // Using radius for circle to maintain aspect ratio
                    width: 95.r, // Using radius for circle to maintain aspect ratio
                    child: Text(
                      'bankingTran_button'.tr,
                      style: TextStyle(
                        color: const Color(0xffB4E60D),
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.normal,
                        fontSize: 14.sp, // Adjusted font size
                      ),
                    ),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: const Color(0xff2B3038).withOpacity(1),
                      boxShadow: [
                        BoxShadow(
                          spreadRadius: 5,
                          blurRadius: 14,
                          color: Colors.black.withOpacity(0.16),
                          offset: const Offset(6.0, 8.0),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget bg() {
    return Column(
      children: <Widget>[
        Container(
          color: const Color(0xff141322),
          height: 0.12.sh, // 25% of screen height
          child: Container(
            padding: EdgeInsets.only(bottom: 15.h),
            alignment: Alignment.bottomCenter,
            width: 1.sw, // Full screen width
            child: Text(
              'bankingTran_title'.tr,
              style: TextStyle(
                fontFamily: 'Proxima Nova',
                color: const Color(0xffFFFFFF).withOpacity(1),
                fontSize: 22.sp, // Adjusted font size
                fontWeight: FontWeight.w500, // Slightly bolder
              ),
            ),
          ),
        ),
        Expanded(
          child: Stack(
            children: [
              SvgPicture.string(
                '<svg viewBox="0.0 256.0 1080.0 1826.8" ><defs><linearGradient id="gradient" x1="0.5" y1="0.298858" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#ffffffff"  /><stop offset="1.0" stop-color="#fff5f5f5"  /></linearGradient></defs><path transform="translate(0.0, 256.0)" d="M 0 0 L 1080 0 L 1080 1826.********* L 0 1826.********* L 0 0 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                fit: BoxFit.fill,
                height: 1.sh,
              ),
              RefreshIndicator(
                onRefresh: _refreshData,
                color: const Color(0xffB4E60D), // Lemon color to match app theme
                backgroundColor: Colors.white,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(), // Important for RefreshIndicator to work
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: 0.07.sh, // 15% of screen height
                      left: 0.15.sw, // 15% of screen width
                      right: 0.05.sw, // 5% of screen width
                    ),
                    child: body(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget body() {
    return Column(
      children: [
        Row(
          children: [
            Container(
              alignment: Alignment.bottomLeft,
              width: 0.25.sw, // 25% of screen width
              child: Text(
                'receipt_from'.tr,
                style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: 'Proxima Nova',
                  color: const Color(0xff6C6B6D).withOpacity(0.5),
                  fontSize: 12.sp, // Adjusted font size
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
            Expanded(
              child: Container(
                alignment: Alignment.bottomLeft,
                child: Obx(() => Text(
                  transferCtrl.fromName.value != 'no'
                      ? transferCtrl.fromName.value
                      : transferCtrl.nameFrom.value.isNotEmpty
                      ? transferCtrl.nameFrom.value.length > 10
                      ? "${transferCtrl.nameFrom.value.substring(0, 5)}...${transferCtrl.nameFrom.value.substring(transferCtrl.nameFrom.value.length - 5)}"
                      : transferCtrl.nameFrom.value
                      : 'Your Account',
                  style: TextStyle(
                    color: const Color(0xff707071),
                    fontFamily: 'Proxima Nova',
                    fontWeight: FontWeight.normal,
                    fontSize: 14.sp, // Adjusted font size
                  ),
                  overflow: TextOverflow.ellipsis,
                )),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        Row(
          children: [
            Container(width: 0.25.sw), // 25% of screen width
            Container(
              child: Image.asset(
                'assets/image/down_arrow.png',
                height: 40.h, // Fixed height
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        Row(
          children: [
            Container(
              alignment: Alignment.bottomLeft,
              width: 0.25.sw, // 25% of screen width
              child: Text(
                'receipt_to'.tr,
                style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: 'Proxima Nova',
                  color: const Color(0xff6C6B6D).withOpacity(0.5),
                  fontSize: 12.sp, // Adjusted font size
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
            Expanded(
              child: Container(
                alignment: Alignment.bottomLeft,
                child: Obx(() => Text(
                  transferCtrl.destName.value != 'no'
                      ? transferCtrl.destName.value
                      : transferCtrl.titleName.value.isNotEmpty &&
                      transferCtrl.titleName.value.substring(0, 1) != '0'
                      ? transferCtrl.titleName.value
                      : transferCtrl.nameTO.value.isNotEmpty
                      ? transferCtrl.nameTO.value.length > 10
                      ? "${transferCtrl.nameTO.value.substring(0, 5)}...${transferCtrl.nameTO.value.substring(transferCtrl.nameTO.value.length - 5)}"
                      : transferCtrl.nameTO.value
                      : '',
                  style: TextStyle(
                    color: const Color(0xff707071),
                    fontFamily: 'Proxima Nova',
                    fontWeight: FontWeight.normal,
                    fontSize: 14.sp, // Adjusted font size
                  ),
                  overflow: TextOverflow.ellipsis,
                )),
              ),
            ),
          ],
        ),
        SizedBox(height: 30.h),
        Row(
          children: [
            Container(
              alignment: Alignment.bottomLeft,
              width: 0.25.sw, // 25% of screen width
              child: Text(
                'receipt_amount'.tr,
                style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: 'Proxima Nova',
                  color: const Color(0xff6C6B6D).withOpacity(0.5),
                  fontSize: 12.sp, // Adjusted font size
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
            Expanded(
              child: Container(
                alignment: Alignment.bottomLeft,
                child: Obx(() => Row(
                  children: [
                    Text(
                      transferCtrl.isVending.value
                          ? transferCtrl.formatter.format(double.parse(transferCtrl.amount.value)) + ' LIKE'
                          : transferCtrl.formatter.format(
                              double.parse(transferCtrl.amount.value) *
                                  transferCtrl.rateCurrency.value *
                                  transferCtrl.rate.value
                          ) + ' LIKE',
                      style: TextStyle(
                        color: const Color(0xff707071),
                        letterSpacing: 0.2,
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.normal,
                        fontSize: 14.sp, // Adjusted font size
                      ),
                    ),
                    SizedBox(width: 5.w),
                    Text(
                      '(${transferCtrl.formatter.format(double.parse(transferCtrl.amount.value))} ${transferCtrl.symbol.value})',
                      style: TextStyle(
                        color: const Color(0xff707071).withOpacity(0.7),
                        letterSpacing: 0.2,
                        fontFamily: 'Proxima Nova',
                        fontWeight: FontWeight.normal,
                        fontSize: 12.sp, // Smaller font size for original amount
                      ),
                    ),
                  ],
                ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        Row(
          children: [
            Container(
              alignment: Alignment.bottomLeft,
              width: 0.25.sw, // 25% of screen width
              child: Text(
                'receipt_fee'.tr,
                style: TextStyle(
                  letterSpacing: 1,
                  fontFamily: 'Proxima Nova',
                  color: const Color(0xff6C6B6D).withOpacity(0.5),
                  fontSize: 12.sp, // Adjusted font size
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
            Expanded(
              child: Container(
                alignment: Alignment.bottomLeft,
                child: Obx(() => Text(
                  transferCtrl.fee.value,
                  style: TextStyle(
                    color: const Color(0xff707071),
                    letterSpacing: 0.2,
                    fontFamily: 'Proxima Nova',
                    fontWeight: FontWeight.normal,
                    fontSize: 14.sp, // Adjusted font size
                  ),
                )),
              ),
            ),
          ],
        ),
        SizedBox(height: 40.h),
        Row(
          children: [
            Container(width: 0.25.sw), // 25% of screen width
            GestureDetector(
              onTap: () {
                setState(() {
                  selected = !selected;
                  print(selected);
                  if (selected) {
                    myFocusNode.requestFocus();
                  } else {
                    transferCtrl.noteController.clear();
                    FocusScope.of(context).unfocus();
                  }
                });
              },
              child: Row(
                children: <Widget>[
                  Padding(
                    padding: EdgeInsets.only(bottom: 2.h),
                    child: Container(
                      height: 17.r, // Using radius for consistent sizing
                      width: 17.r, // Using radius for consistent sizing
                      decoration: BoxDecoration(
                        color: selected ? LikeWalletAppTheme.lemon : Colors.transparent,
                        border: Border.all(
                          width: 1.w,
                          color: selected ? Colors.transparent : LikeWalletAppTheme.gray,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Text(
                    selected
                        ? 'bankingTran_note'.tr
                        : 'bankingTran_add_note'.tr,
                    style: TextStyle(
                      color: const Color(0xff6C6B6D).withOpacity(0.5),
                      letterSpacing: 1,
                      fontFamily: 'Proxima Nova',
                      fontWeight: FontWeight.w100,
                      fontSize: 12.sp, // Adjusted font size
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 15.h),
        Row(
          children: [
            Container(width: 0.25.sw), // 25% of screen width
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: 0.15.sw), // 15% of screen width
                child: TextFormField(
                  controller: transferCtrl.noteController,
                  style: TextStyle(
                    fontSize: 12.sp, // Adjusted font size
                    color: const Color(0xff707071),
                    fontWeight: FontWeight.w100,
                    fontFamily: 'Proxima Nova',
                  ),
                  maxLines: 4,
                  inputFormatters: [LengthLimitingTextInputFormatter(88)],
                  keyboardType: TextInputType.text,
                  focusNode: myFocusNode,
                  decoration: const InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(50.0),
                      ),
                      borderSide: BorderSide.none,
                    ),
                    fillColor: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
        // Add some bottom padding for better scrolling
        SizedBox(height: 50.h),
      ],
    );
  }
}
