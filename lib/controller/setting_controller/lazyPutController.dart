import 'package:get/get.dart';
import 'package:likewallet/controller/buyLikepointController/buyLikepointController.dart';
import 'package:likewallet/controller/historyController/historyController.dart';
import 'package:likewallet/controller/lockNEarn/lockNEarnController.dart';
import 'package:likewallet/controller/newsController/newsController.dart';
import 'package:likewallet/controller/otherController/configurationController.dart';
import 'package:likewallet/controller/otherController/drawerController.dart';
import 'package:likewallet/controller/otherController/keyMnemoryController.dart';
import 'package:likewallet/controller/otherController/logoStoreController.dart';
import 'package:likewallet/controller/permissionCheck/permissionCheckController.dart';
import 'package:likewallet/controller/profile/loginCotroller.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/rewardController/rewardController.dart';
import 'package:likewallet/controller/scanController/scanController.dart';
import 'package:likewallet/controller/slipController/slipController.dart';
import 'package:likewallet/controller/transferController/onContractController.dart';
import 'package:likewallet/controller/transferController/transferController.dart';
import 'package:likewallet/controller/walletController/walletDataController.dart';


class AppBindings {
  static Future<void> lazyLoadControllers() async {
    // Helper function to register only if not already registered
    void register<T>(T Function() creator) {
      if (!Get.isRegistered<T>()) {
        Get.lazyPut<T>(creator);
      }
    }

    // Register Controllers
    register(() => LoginController());
    register(() => ProfileController());
    register(() => CheckAboutController());
    register(() => ConfigurationController());
    register(() => KeyMNemoryController());
    register(() => WalletDataController());
    register(() => LogoStoreController());
    register(() => TransferController());
    register(() => ContactController());
    register(() => ScanController());
    register(() => RewardController());
    register(() => SlipController());
    register(() => NewsController());
    register(() => BuyLikepointController());
    register(() => HistoryController());
    register(() => DrawerOwnController());
    register(() => LockNEarnController());

    // Call initial async methods
    await Get.find<ProfileController>().getCurrentUser();
    await Get.find<KeyMNemoryController>().initializeEncryptionKeys();
    await Get.find<RewardController>().getNewReward();
    await Get.find<HistoryController>().getHistory();
    await Get.find<DrawerOwnController>().checkFirst();
    await Get.find<LockNEarnController>().initializeData();
    await Get.find<NewsController>().checkNewsPermission();
  }
}

