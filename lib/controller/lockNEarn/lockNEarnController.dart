import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class LockNEarnController extends GetxController {
  // Observable variables
  final selected = true.obs;
  final isLoading = false.obs;
  final success = false.obs;
  final unlockAll = false.obs;
  final remainingTime = '00:00:00'.obs;
  final amountUnlock = '200'.obs;
  final lockedBalance = '0'.obs;

  // Text controllers
  final amountLock = TextEditingController();
  final amountUnLock = TextEditingController();

  // Carousel images
  final List<String> carouselImages = [
    'assets/image/locklike/1.png',
    'assets/image/locklike/2.png',
    'assets/image/locklike/3.png',
  ];

  @override
  void onInit() {
    super.onInit();
    initializeData();
  }

  @override
  void onClose() {
    amountLock.dispose();
    amountUnLock.dispose();
    super.onClose();
  }

  // Initialize data
  void initializeData() {
    // Load initial data here
    loadBalanceData();
  }

  // Load balance data
  void loadBalanceData() {
    isLoading.value = true;
    
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      // Mock data - replace with actual API calls
      amountUnlock.value = '1,000';
      lockedBalance.value = '500';
      isLoading.value = false;
    });
  }

  // Toggle between lock and unlock mode
  void toggleMode(bool isLockMode) {
    selected.value = isLockMode;
    // Clear input when switching modes
    if (isLockMode) {
      amountUnLock.clear();
    } else {
      amountLock.clear();
    }
  }

  // Set all amount for locking
  void setAllAmount() {
    if (selected.value) {
      // Lock mode - set all available balance
      amountLock.text = amountUnlock.value;
    } else {
      // Unlock mode - set all locked balance
      amountUnLock.text = lockedBalance.value;
    }
  }

  // Get available balance (total - locked)
  String getAvailableBalance() {
    try {
      final total = int.parse(amountUnlock.value.replaceAll(',', ''));
      final locked = int.parse(lockedBalance.value.replaceAll(',', ''));
      final available = total - locked;
      return NumberFormat("#,###").format(available);
    } catch (e) {
      return '0';
    }
  }

  // Validate lock amount
  bool validateLockAmount() {
    if (amountLock.text.isEmpty) return false;
    
    try {
      final lockAmount = int.parse(amountLock.text.replaceAll(',', ''));
      final availableAmount = int.parse(getAvailableBalance().replaceAll(',', ''));
      
      return lockAmount > 0 && lockAmount <= availableAmount;
    } catch (e) {
      return false;
    }
  }

  // Validate unlock amount
  bool validateUnlockAmount() {
    if (amountUnLock.text.isEmpty) return false;
    
    try {
      final unlockAmount = int.parse(amountUnLock.text.replaceAll(',', ''));
      final lockedAmount = int.parse(lockedBalance.value.replaceAll(',', ''));
      
      return unlockAmount > 0 && unlockAmount <= lockedAmount;
    } catch (e) {
      return false;
    }
  }

  // Execute lock operation
  Future<void> executeLock() async {
    if (!validateLockAmount()) {
      Get.snackbar(
        'Error',
        'Invalid lock amount',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    isLoading.value = true;
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Update balances
      final lockAmount = int.parse(amountLock.text.replaceAll(',', ''));
      final currentLocked = int.parse(lockedBalance.value.replaceAll(',', ''));
      final newLockedBalance = currentLocked + lockAmount;
      
      lockedBalance.value = NumberFormat("#,###").format(newLockedBalance);
      amountLock.clear();
      
      success.value = true;
      
      Get.snackbar(
        'Success',
        'Successfully locked $lockAmount LIKE',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Hide success message after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        success.value = false;
      });
      
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to lock amount: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Execute unlock operation
  Future<void> executeUnlock() async {
    if (!validateUnlockAmount()) {
      Get.snackbar(
        'Error',
        'Invalid unlock amount',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    isLoading.value = true;
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Update balances
      final unlockAmount = int.parse(amountUnLock.text.replaceAll(',', ''));
      final currentLocked = int.parse(lockedBalance.value.replaceAll(',', ''));
      final newLockedBalance = currentLocked - unlockAmount;
      
      lockedBalance.value = NumberFormat("#,###").format(newLockedBalance);
      amountUnLock.clear();
      
      success.value = true;
      
      Get.snackbar(
        'Success',
        'Successfully unlocked $unlockAmount LIKE',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Hide success message after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        success.value = false;
      });
      
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to unlock amount: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Handle action button press
  void handleActionButton() {
    if (selected.value) {
      executeLock();
    } else {
      executeUnlock();
    }
  }
}
