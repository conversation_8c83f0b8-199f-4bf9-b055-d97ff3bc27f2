import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/otherController/logoStoreController.dart';
import 'package:likewallet/controller/slipController/slipController.dart';
import 'package:likewallet/controller/walletController/walletDataController.dart';
import 'package:likewallet/controller/web3/web3ToolsController.dart';
import 'package:likewallet/model/contacts/contactsModel.dart';
import 'package:likewallet/controller/transferController/onContractController.dart';
import 'package:likewallet/model/contacts/shopListModel.dart';
import 'package:likewallet/model/tansferModel/vendingModel.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';
import 'package:likewallet/view/transferPoint/transactionSlip.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:likewallet/controller/otherController/configurationController.dart';
import 'package:firebase_auth/firebase_auth.dart';

class TransferController extends GetxController {

  RxInt shopID = 0.obs;
  List group = [];
  List<ShopList> search = [];
  List<ShopList> _list = [];
  String toAddress = 'noaddress';
  List<Contacts> getContactMobile = [];
  late OnContact contact;

  RxBool isLoading = false.obs;

  // Contact loading state
  RxBool contactLoading = false.obs;

  // Colors for contacts
  final List<Color> colors = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple
  ];

  // Text controllers
  final TextEditingController addressText = TextEditingController();
  final TextEditingController phoneNumber = TextEditingController();
  final TextEditingController amountSend = TextEditingController();
  final TextEditingController noteController = TextEditingController();
  RxString selectedInput = ''.obs;

  // Transaction details for confirmation page
  RxString fromName = 'no'.obs;
  RxString nameFrom = ''.obs;
  RxString destName = 'no'.obs;
  RxString titleName = ''.obs;
  RxString nameTO = ''.obs;
  RxString amount = '0'.obs;
  RxBool isVending = false.obs;
  RxDouble rateCurrency = 0.0.obs;
  RxDouble rate = 0.0.obs;
  RxString fee = '0 LIKE'.obs;
  RxString selectedCurrency = 'THB'.obs;

  // Currency symbol with default value
  RxString symbol = 'THB'.obs;

  // Available currency symbols - using RxList for reactivity
  RxList<String> availableSymbols = <String>['THB', 'LAK', 'VND', 'USD', 'GOLD', 'LIKE'].obs;

  // Default symbols to use if none are stored
  final List<String> defaultSymbols = ['THB', 'LAK', 'VND', 'USD', 'GOLD', 'LIKE'];

  var selectedCurrencyJson = {}.obs;
  final List<Map<String, String>> currencies = const [
    {"key": "usd", "label": "USD - US dollar"},
    {"key": "thb", "label": "บาท - ไทยบาท"},
    {"key": "lak", "label": "ກີບ - ລາວກີບ"},
    {"key": "gold", "label": "(ทอง) กรัม"},
    {"key": "like", "label": "LIKE"},
  ];

  // Format for numbers
  final NumberFormat formatter = NumberFormat("#,##0.00", "en_US");

  @override
  void onInit() {
    super.onInit();
    // Load saved available symbols from storage
    loadAvailableSymbols();
    // Load saved symbol from storage
    loadSavedSymbol();
    // Load saved currency ratio
    loadSavedRatio();
  }

  // Load saved currency ratio from storage
  void loadSavedRatio() {
    final savedRatio = Storage.get<double>(StorageKeys.currencyRatio);
    if (savedRatio != null) {
      rate.value = savedRatio;
    } else {
      // Set default ratio based on current symbol
      if (symbol.value == 'LIKE') {
        rate.value = 1.0; // 1:1 ratio for LIKE
      } else {
        rate.value = 100.0; // 1:100 ratio for other currencies
      }
      // Save the default ratio
      Storage.save(StorageKeys.currencyRatio, rate.value);
    }
  }

  void changeSelectedInput(String value) {
    addressText.text = '';
    phoneNumber.text = '';
    selectedInput.value = value;
    update();
  }

  void updateSelectedCurrencyJson() {
    final found = currencies.firstWhere(
          (item) => item['key'] == selectedCurrency.value,
      orElse: () => {},
    );

    if (found.isNotEmpty) {
      selectedCurrencyJson.value = found;
    } else {
      selectedCurrencyJson.value = {}; // หรือค่าตั้งต้นอื่นๆ
    }
  }

  void setAddress(String address) {
    print("setAddress");
    print(address);
    addressText.text = address;
    selectedInput.value = 'address';
    update();
  }

  // Get display text for address field - shows title if address matches logoCtrl list
  String getAddressDisplayText() {
    try {
      // Get LogoStoreController instance
      final logoCtrl = Get.find<LogoStoreController>();

      // Check if addressText matches any item in logoCtrl list
      for (int i = 0; i < logoCtrl.list.length; i++) {
        if (addressText.text.trim() == logoCtrl.list[i].address.toString().trim()) {
          return logoCtrl.list[i].title.toString();
        }
      }
    } catch (e) {
      print('Error getting logo controller: $e');
    }

    // Return the actual address text if no match or error
    return addressText.text;
  }
  // Load available symbols from storage
  void loadAvailableSymbols() {
    final savedSymbols = Storage.get<List<dynamic>>(StorageKeys.availableSymbols);
    if (savedSymbols != null && savedSymbols.isNotEmpty) {
      // Convert dynamic list to string list
      availableSymbols.value = savedSymbols.map((e) => e.toString()).toList();
    } else {
      // Use default symbols if none are stored
      availableSymbols.value = List.from(defaultSymbols);
      // Save default symbols to storage
      saveAvailableSymbols();
    }
  }

  // Save available symbols to storage
  void saveAvailableSymbols() {
    Storage.save(StorageKeys.availableSymbols, availableSymbols.toList());
  }

  // Load saved symbol from storage
  void loadSavedSymbol() {
    final savedSymbol = Storage.get<String>(StorageKeys.currency);
    if (savedSymbol != null && savedSymbol.isNotEmpty) {
      // Check if the saved symbol is in the available symbols list
      if (availableSymbols.contains(savedSymbol)) {
        symbol.value = savedSymbol;
        selectedCurrency.value = savedSymbol;
        updateSelectedCurrencyJson();
        // Update rate based on loaded symbol
        updateRateForSymbol(savedSymbol);
      } else if (availableSymbols.isNotEmpty) {
        // If the saved symbol is not available, use the first available symbol
        symbol.value = availableSymbols[0];
        selectedCurrency.value = availableSymbols[0];
        updateSelectedCurrencyJson();
        // Save the new symbol
        Storage.save(StorageKeys.currency, availableSymbols[0]);
        // Update rate based on the new symbol
        updateRateForSymbol(availableSymbols[0]);
      }
    } else if (availableSymbols.isNotEmpty) {
      // If no symbol is saved, use the first available symbol
      symbol.value = availableSymbols[0];
      selectedCurrency.value = availableSymbols[0];
      updateSelectedCurrencyJson();
      // Save the symbol
      Storage.save(StorageKeys.currency, availableSymbols[0]);
      // Update rate based on the symbol
      updateRateForSymbol(availableSymbols[0]);
    }
  }

  // Save symbol to storage
  void saveSymbol(String newSymbol) {
    if (availableSymbols.contains(newSymbol)) {
      symbol.value = newSymbol;
      selectedCurrency.value = newSymbol;
      updateSelectedCurrencyJson();
      Storage.save(StorageKeys.currency, newSymbol);

      // Update rate based on new symbol
      updateRateForSymbol(newSymbol);
    } else if (newSymbol.isNotEmpty) {
      // If the symbol is not in the available symbols list, add it
      addSymbol(newSymbol);
      symbol.value = newSymbol;
      selectedCurrency.value = newSymbol;
      updateSelectedCurrencyJson();
      Storage.save(StorageKeys.currency, newSymbol);

      // Update rate based on new symbol
      updateRateForSymbol(newSymbol);
    }
  }

  // Add a new symbol to available symbols
  void addSymbol(String newSymbol) {
    if (newSymbol.isNotEmpty && !availableSymbols.contains(newSymbol)) {
      availableSymbols.add(newSymbol);
      saveAvailableSymbols();
    }
  }

  // Remove a symbol from available symbols
  void removeSymbol(String symbolToRemove) {
    // Don't remove the currently selected symbol
    if (symbolToRemove != symbol.value && availableSymbols.contains(symbolToRemove)) {
      // Don't remove default symbols
      if (!defaultSymbols.contains(symbolToRemove)) {
        availableSymbols.remove(symbolToRemove);
        saveAvailableSymbols();
      }
    }
  }

  // Update rate based on symbol
  void updateRateForSymbol(String symbolValue) {
    // Check if we have a saved ratio for this symbol
    double? savedRatio = _getSavedRatioForSymbol(symbolValue);

    if (savedRatio != null) {
      rateCurrency.value = savedRatio;
    } else {
      // Use default rates if no saved ratio exists
      switch (symbolValue) {
        case 'THB':
          rateCurrency.value = 1.0;
          break;
        case 'USD':
          rateCurrency.value = 30.0; // Example rate
          break;
        case 'LAK':
          rateCurrency.value = 0.003; // Example rate
          break;
        case 'VND':
          rateCurrency.value = 0.0013; // Example rate
          break;
        case 'GOLD':
          rateCurrency.value = 30000.0; // Example rate
          break;
        case 'LIKE':
          rateCurrency.value = 1.0;
          break;
        default:
          rateCurrency.value = 1.0;
      }

      // Save the rate for future use
      _saveRatioForSymbol(symbolValue, rateCurrency.value);
    }

    // Set vending flag if LIKE is selected
    // isVending.value = (symbolValue == 'LIKE');

    // Apply the correct ratio for calculations
    if (symbolValue == 'LIKE') {
      // For LIKE, use 1:1 ratio
      rate.value = 1.0;
    } else {
      // For other currencies, use 1:100 ratio
      rate.value = 100.0;
    }

    // Save the current rate
    Storage.save(StorageKeys.currencyRatio, rate.value);

    update();
  }

  // Get saved ratio for a symbol
  double? _getSavedRatioForSymbol(String symbol) {
    final Map<String, dynamic>? savedRatios = Storage.get<Map<String, dynamic>>('ratios_$symbol');
    if (savedRatios != null && savedRatios.containsKey(symbol)) {
      return savedRatios[symbol];
    }
    return null;
  }

  // Save ratio for a symbol
  void _saveRatioForSymbol(String symbol, double ratio) {
    Map<String, dynamic> savedRatios = Storage.get<Map<String, dynamic>>('ratios_$symbol') ?? {};
    savedRatios[symbol] = ratio;
    Storage.save('ratios_$symbol', savedRatios);
  }

  Future<dynamic> getQuickpayShop() async {
    try{
      print('getQuickayShop');


      var response = await AppApi.post('${AppEnv.apiUrl}/listQucikpayShop', {
        "apiKey": AppEnv.APIKEY,
        "secretKey": AppEnv.SECRETKEY,
      });
      // print(response.body);
      for (Map<String, dynamic> user in response["result"]) {
        _list.add(ShopList.fromJson(user));
        search = _list;
      }
      for (var i = 0; i < response["group"].length; i++) {
        group.add(response["group"][i]);
      }

      return [_list, group];
    }catch(e){
      print("Error getting quickpay shop: $e");
      return [];
    }
  }

  void setPay() async {
    var data = await getQuickpayShop();
      _list = data[0];
      search = data[0];
      group = data[1];

      // if not error do below

    for (var i = 0; i < _list.length; i++) {
      if (_list[i].running == shopID) {
        toAddress = _list[i].address.toString().trim();
        addressText.text = _list[i].title.toString();
        break;
      }
    }
    update();
  }

  // Prepare transaction data for confirmation page
  void prepareTransactionData() {
    print("🔄 prepareTransactionData");

    // Set amount from input
    if (amountSend.text.isNotEmpty) {
      amount.value = amountSend.text;
      print("✅ amount set to: ${amount.value}");
    } else {
      print("⚠️ amountSend is empty");
    }

    // Check if this is a vending transaction
    if (isVending.value == true) {
      print("🧃 is vending transaction");
      fee.value = '0.0001 LIKE';
      print("💰 fee set to fixed vending fee: ${fee.value}");
    } else {
      print("💼 is normal transaction");

      // Make sure selectedCurrency matches the symbol
      selectedCurrency.value = symbol.value;
      updateSelectedCurrencyJson();
      print("🔁 selectedCurrency set to: ${selectedCurrency.value}");

      // Update rate based on symbol
      updateRateForSymbol(symbol.value);
      print("📈 called updateRateForSymbol with: ${symbol.value}");

      // Set recipient information
      if (toAddress != 'noaddress') {
        nameTO.value = toAddress;
        print("📮 toAddress used: ${nameTO.value}");

        if (addressText.text.isNotEmpty) {
          titleName.value = addressText.text;
          print("🏷️ titleName set from addressText: ${titleName.value}");
        } else {
          print("⚠️ addressText is empty");
        }
      } else if (phoneNumber.text.isNotEmpty) {
        nameTO.value = phoneNumber.text;
        print("📱 nameTO set from phoneNumber: ${nameTO.value}");
      } else {
        print("❌ No recipient (neither toAddress nor phoneNumber)");
      }

      // Calculate fee based on amount and currency
      double amountValue = double.tryParse(amount.value) ?? 0;
      print("💵 Parsed amountValue: $amountValue");

      double feeAmount;
      if (symbol.value == 'LIKE') {
        feeAmount = amountValue * 0.01;
        print("💰 Calculated LIKE fee: $feeAmount");
      } else {
        feeAmount = (amountValue * rateCurrency.value * rate.value) * 0.01;
        print(
            "💰 Calculated non-LIKE fee: $feeAmount (rateCurrency: ${rateCurrency.value}, rate: ${rate.value})");
      }

      if (feeAmount > 0) {
        fee.value = '${formatter.format(feeAmount)} LIKE';
        print("✅ Final fee set to: ${fee.value}");
      } else {
        fee.value = '0 LIKE';
        print("💡 Final fee is zero");
      }
    }

    update();
    print("🔁 Done prepareTransactionData, called update()");
  }


  // Clear transaction data after completion
  void clearTransactionData() {
    addressText.clear();
    phoneNumber.clear();
    amountSend.clear();
    noteController.clear();
    selectedInput.value = '';
    toAddress = 'noaddress';
    amount.value = '0';
    nameTO.value = '';
    titleName.value = '';
    destName.value = 'no';
    fee.value = '0 LIKE';
    update();
  }

  Future<List<Contacts>> getContacts({required BuildContext context}) async {
    List<String> phoneContact = [];
    getContactMobile.clear();

    // Update loading state
    contactLoading.value = true;
    update();

    // Check permission first
    bool hasPermission = await contact.permissionContact();

    if (hasPermission) {
      // Get contacts from device
      final deviceContacts = await FlutterContacts.getContacts(withProperties: true);

      // Process each contact's phone numbers
      for (var element in deviceContacts) {
        for (var phone in element.phones) {
          if (phone.number.isNotEmpty && phone.number.toString().startsWith('0')) {
            final p = phone.number.toString();
            // Add different country code variants
            phoneContact.add(p
                .replaceFirst('0', '+66')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
            phoneContact.add(p
                .replaceFirst('0', '+885')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
            phoneContact.add(p
                .replaceFirst('0', '+888')
                .replaceAll(' ', '')
                .replaceAll('-', ''));
          }
        }
      }

      int colorIndex = 0;

      // Query Firestore for matching phone numbers
      for (var phoneNumber in phoneContact) {
        try {
          QuerySnapshot<Map<String, dynamic>> queryResult = await FirebaseFirestore.instance
              .collection('addressDNS')
              .where('phoneNumber', isEqualTo: phoneNumber)
              .get();

          for (var doc in queryResult.docs) {
            if (colorIndex % colors.length == 0) {
              colorIndex = 0;
            }

            var contactData = {
              'name': doc.data()['name'],
              'phoneNumber': doc.data()['phoneNumber'],
              'address': doc.data()['address'],
              'color': colors[colorIndex++],
            };

            getContactMobile.add(Contacts.fromJson(contactData));
            update();
          }
        } catch (e) {
          print('Error querying Firestore: $e');
        }
      }

      print('Found ${getContactMobile.length} contacts in Firestore');
    } else {
      // Show permission dialog
      showDialog(
          context: context,
          builder: (BuildContext context) => CupertinoAlertDialog(
            title: Text('Permissions error'),
            content: Text('Please enable contacts access '
                'permission in system settings'),
            actions: <Widget>[
              CupertinoDialogAction(
                child: Text('OK'),
                onPressed: () => Navigator.of(context).pop(),
              )
            ],
          ));
    }

    // Update loading state
    contactLoading.value = false;
    update();

    return getContactMobile;
  }

  // Vending data
  Rx<Vending?> vendingData = Rx<Vending?>(null);



  // Set vending data
  void setVendingData(Vending vending) {
    vendingData.value = vending;

    // Set transaction details from vending data
    addressText.text = vending.addr;
    amountSend.text = vending.amount.toString();
    titleName.value = vending.addr;

    update();
  }

  void allowVending() {
    isVending.value = true;
    print("isVending set to true");
    update();
  }



  // Send transaction method
  Future<bool> sendTransaction() async {
    try {
      // isLoading.value = true;

      // Check if this is a vending transaction
      if (isVending.value && vendingData.value != null) {
        return await _sendVendingTransaction();
      } else {
        print("no vender");

        return await signTransaction();
        // return false;
      }
    } catch (e) {
      print('Transaction error: $e');
      isLoading.value = false;
      return false;
    }
  }

  // Send vending transaction
  Future<bool> _sendVendingTransaction() async {
    try {
      // Get the private key from secure storage
      final configCtrl = Get.find<ConfigurationController>();
      String privateKey = await configCtrl.getPrivateKey();

      if (privateKey.isEmpty) {
        Get.snackbar(
          'Error',
          'Private key not found. Please log in again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return false;
      }

      // Get vending data
      Vending vending = vendingData.value!;

      // Get contract information from Firestore
      DocumentSnapshot<Map<String, dynamic>> vendingDoc = await FirebaseFirestore.instance
          .collection('vending')
          .doc(vending.function)
          .get();

      if (!vendingDoc.exists) {
        Get.snackbar(
          'Error',
          'Vending contract not found',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return false;
      }

      String contractVending = vendingDoc.data()?['contract'] ?? '';
      String abiVending = vendingDoc.data()?['abi'] ?? '';

      if (contractVending.isEmpty || abiVending.isEmpty) {
        Get.snackbar(
          'Error',
          'Invalid vending contract information',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return false;
      }

      // Show loading message
      EasyLoading.show(
        status: 'Processing vending transaction...',
        maskType: EasyLoadingMaskType.black,
        dismissOnTap: false,
      );

      // Call API to send vending transaction
      try {
        var response = await AppApi.post(
          "${AppEnv.apiUrl}/transaction/vending",
          {
            "apiKey": AppEnv.APIKEY,
            "secretKey": AppEnv.SECRETKEY,
            "privateKey": privateKey,
            "contract": contractVending,
            "abi": abiVending,
            "vending": {
              "machine_id": vending.machine_id,
              "amount": vending.amount.toString(),
              "addr": vending.addr,
              "ref_code": vending.ref_code,
              "payment_id": vending.payment_id,
              "function": vending.function
            }
          }
        );

        if (response['status'] == 'success' || response['statusCode'] == 200) {
          // Transaction successful
          String txHash = response['txHash'] ?? response['result'] ?? '';

          // Save transaction to history
          await _saveTransactionToHistory(txHash, vending.addr, double.parse(vending.amount.toString()));

          // Show transaction slip
          await showTransactionSlip(
            txHash: txHash,
            recipient: vending.addr,
            recipientName: vending.addr,
            amount: vending.amount.toString(),
            isVendingTx: true,
          );

          // Clear data after successful transaction
          clearTransactionData();

          // Reset vending state
          vendingData.value = null;
          isVending.value = false;

          EasyLoading.dismiss();
          Get.snackbar(
            'Success',
            'Vending transaction completed successfully',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

          isLoading.value = false;
          return true;
        } else {
          throw Exception(response['message'] ?? 'Vending transaction failed');
        }
      } catch (apiError) {
        EasyLoading.dismiss();
        Get.snackbar(
          'Error',
          'Vending transaction failed: ${apiError.toString()}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return false;
      }
    } catch (e) {
      print('Vending transaction error: $e');
      EasyLoading.dismiss();
      Get.snackbar(
        'Error',
        'Vending transaction failed: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      isLoading.value = false;
      return false;
    }
  }

  // Sign transaction (normal transaction)
  Future<bool> signTransaction() async {
    print('Start signTransaction');
    try {

      final walletController = Get.isRegistered<WalletDataController>()
          ? Get.find<WalletDataController>()
          : Get.put(WalletDataController());

      final web3Tools = Get.isRegistered<Web3toolsController>()
          ? Get.find<Web3toolsController>()
          : Get.put(Web3toolsController());

      final storeCtrl = Get.isRegistered<LogoStoreController>()
          ? Get.find<LogoStoreController>()
          : Get.put(LogoStoreController());


      final balance = walletController.totalBalance.value;
      var contract = storeCtrl.list[0].contract ?? 'no';
      var abi = storeCtrl.list[0].contract != null ? storeCtrl.list[0].abi : 'no';
      var callFunction = storeCtrl.list[0].contract != null ? storeCtrl.list[0].callFunction : 'no';

      final requiredAmount =
          double.parse(amount.toString()) * rateCurrency.value * rate.value;

      print('Required amount: $requiredAmount');
      if (balance < requiredAmount) {
        EasyLoading.dismiss();
        // showShortToast(
        //   '${'not_enough'.tr}${'not_enough_tail'.tr} $balance LIKE',
        //   Colors.red,
        // );
        return true;
      }

      print('Available to transfer');

      // final isLoggedIn = await Logon.checkLogin();
      // if (!isLoggedIn) {
      //   EasyLoading.dismiss();
      //   showShortToast('Login required', Colors.red);
      //   return;
      // }

      print('User logged in');

      final mnemonic = await Storage.get(StorageKeys.mnemonic);
      final pketh = await Storage.get(StorageKeys.privateKey);

      final amountStr = requiredAmount.toString();
      // final toAddress = nameTO.trim();

      var toAddress = await walletController.getAddressByPhone(phoneNumber.text.trim()) ?? "0x";

      if(toAddress == '0x' || toAddress.isEmpty) {
        toAddress = addressText.text.trim();
      }

      final note = noteController.text.trim();
      print('toAddress: $toAddress');

      if (contract == 'no' || contract == 'null') {
        print('No contract');
        final tx = await web3Tools.transferMessage(
          pk: pketh,
          to: toAddress,
          value: amountStr,
          message: note,
        );

        if (tx == 'e') {
          EasyLoading.dismiss();

          return false;
        }

        final response = await AppApi.post('${AppEnv.apiUrl}/checkPending',{"tx": tx});

        print(response);
        if (response['statusCode'] == 200) {
          // Show transaction slip
          await showTransactionSlip(
            txHash: tx,
            recipient: toAddress,
            recipientName: titleName.value.isNotEmpty ? titleName.value : toAddress,
            amount: amount.value,
            isVendingTx: false,
          );
        } else {
          EasyLoading.dismiss();
        }
      } else if (contract!.length == 42 && callFunction != 'no' && abi != 'no') {
      // } else if (contract!.length == 42 ) {
        print('Contract detected');

        // final notifyUri = Uri.https(env.apiUrl, '/notifyServeGroup');
        // final notifyBody = {
        //   "token_line": token_line,
        //   "message":
        //   "address: $toAddress\n$fromName กำลังโอนไป => $destName\n$pketh\n$toAddress\n$amountStr\n$note\n$callFunction\n$abi\n$contract",
        // };
        // await http.post(notifyUri, body: notifyBody);

        final tx = await web3Tools.dynamicSendContract(
          pk: pketh,
          to: toAddress,
          value: amountStr,
          message: note,
          callFunction: callFunction!,
          abi: abi!,
          contractAddress: contract,
        );

        print(tx);

        final response = await AppApi.post('${AppEnv.apiUrl}/checkPending',{"tx": tx});

        print("go here");
        print(response);
        if (response['statusCode'] == 200) {
          // Show transaction slip

          var showmeParam = {
            "txHash": tx,
            "recipient": toAddress,
            "recipientName": titleName.value.isNotEmpty ? titleName.value : toAddress,
            "amount": amountStr,
            "isVendingTx": false
          };

          print("showmeParam");
          print(showmeParam);
          EasyLoading.dismiss();
          await showTransactionSlip(
            txHash: tx,
            recipient: toAddress,
            recipientName: titleName.value.isNotEmpty ? titleName.value : toAddress,
            amount: amount.value,
            isVendingTx: false,
          );
        } else {
          EasyLoading.dismiss();
        }
      } else {
        EasyLoading.dismiss();
        print('Unexpected contract condition');
      }

      return false;
    } catch (e) {
      print('Error in signTransaction: $e');
      return false;
    }
  }


  // Helper method to save transaction to history
  Future<void> _saveTransactionToHistory(String txHash, String recipient, double amount) async {
    try {
      final auth = FirebaseAuth.instance;
      String? currentUser = auth.currentUser?.phoneNumber;

      if (currentUser == null || currentUser.isEmpty) return;

      await FirebaseFirestore.instance.collection('transactions').add({
        'txHash': txHash,
        'sender': currentUser,
        'recipient': recipient,
        'amount': amount,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'completed',
        'type': 'transfer',
        'message': noteController.text,
      });
    } catch (e) {
      print('Error saving transaction history: $e');
      // Non-critical error, don't affect the main transaction flow
    }
  }

  // Show transaction slip
  Future<void> showTransactionSlip({
    required String txHash,
    required String recipient,
    required String recipientName,
    required String amount,
    required bool isVendingTx,
  }) async {
    try {
      // Get wallet data controller for sender information
      final walletController = Get.isRegistered<WalletDataController>()
          ? Get.find<WalletDataController>()
          : Get.put(WalletDataController());

      // Initialize slip controller if not already registered
      final slipController = Get.isRegistered<SlipController>()
          ? Get.find<SlipController>()
          : Get.put(SlipController());

      // Get sender address and name
      String senderAddress = walletController.address.value;
      String senderName = fromName.value.isNotEmpty ? fromName.value : senderAddress;

      // Set transaction data in slip controller
      slipController.setTransactionData(
        from: senderAddress,
        to: recipient,
        fromNameStr: senderName,
        toNameStr: recipientName,
        amountStr: amount,
        feeStr: fee.value,
        noteStr: noteController.text,
        txHashStr: txHash,
        currencyStr: symbol.value,
        rateCurrencyValue: rateCurrency.value,
        rateValue: rate.value,
        isVendingValue: isVendingTx,
      );

      // Save transaction to history
      await slipController.saveTransactionToHistory();

      // Navigate to transaction slip page
      // The auto-save and navigation will be handled in the TransactionSlipPage
      await Get.to(() => TransactionSlipPage());

      // Note: We don't need to manually call captureSlip here anymore
      // The TransactionSlipPage will handle auto-saving and navigation
      // based on the autoSaveSlip setting
    } catch (e) {
      print('Error showing transaction slip: $e');
      // Non-critical error, don't affect the main transaction flow
    }
  }
}