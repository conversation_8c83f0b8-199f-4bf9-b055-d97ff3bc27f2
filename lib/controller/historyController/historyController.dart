import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/model/historyModel/transactionModel.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';

class HistoryController extends GetxController {
  RxInt tabSelect = 1.obs;
  final fireStore = FirebaseFirestore.instance;
  ProfileController profileCtrl = Get.find<ProfileController>();

  late List<TransactionModel> all = [];
  late List<TransactionModel> sent = [];
  late List<TransactionModel> receive = [];
  late List<TransactionModel> reward = [];

  RxString selectedFilter = 'All'.obs;
  RxString previousFilter = 'All'.obs;

  Future<void> changeTabSelection(int tab) async {
    if (tabSelect.value != tab) {

      await Future.delayed(Duration(milliseconds: 150));
      tabSelect.value = tab;

    }
  }

  Future<void> getHistory() async {
    try {
      print("getHistory");
      var address = Storage.get(StorageKeys.addressETH) ?? "";
      var token = await profileCtrl.getTokenFirebase();

      Map<String, dynamic> body = {
        "_token": token,
        "address": address,
      };

      var response = await AppApi.post('${AppEnv.apiUrl}/getHistoryNew', body);

      print(response);
      if (response['statusCode'] == 200) {
        List<dynamic> rawList = response['result'];

        // ล้างข้อมูลเดิม (กรณี getHistory ถูกเรียกซ้ำ)
        all.clear();
        sent.clear();
        receive.clear();
        reward.clear();

        for (var item in rawList) {
          var model = TransactionModel.fromJson(item as Map<String, dynamic>);
          all.add(model); // เพิ่มทุกตัวเข้า all

          // แยกตาม title
          switch ((model.title ?? '').toLowerCase()) {
            case 'sent':
              sent.add(model);
              break;
            case 'recv':
              if(model.to == 'Reward'){
                reward.add(model);
              }else{
                receive.add(model);
              }
              break;
          }

        }
        update();
      } else {
        print("Error: ${response['message']}");
      }

      update();
    } catch (e) {
      print("Exception: $e");
    }
  }

}