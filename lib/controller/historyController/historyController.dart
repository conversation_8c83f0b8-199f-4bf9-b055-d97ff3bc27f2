import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/model/historyModel/transactionModel.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';

class HistoryController extends GetxController {
  RxInt tabSelect = 1.obs;
  final fireStore = FirebaseFirestore.instance;
  ProfileController profileCtrl = Get.find<ProfileController>();

  RxList<TransactionModel> all = <TransactionModel>[].obs;
  RxList<TransactionModel> sent = <TransactionModel>[].obs;
  RxList<TransactionModel> receive = <TransactionModel>[].obs;
  RxList<TransactionModel> reward = <TransactionModel>[].obs;

  RxString selectedFilter = 'All'.obs;
  RxString previousFilter = 'All'.obs;

  Future<void> changeTabSelection(int tab) async {
    if (tabSelect.value != tab) {

      await Future.delayed(Duration(milliseconds: 150));
      tabSelect.value = tab;

    }
  }

  Future<void> getHistory() async {
    try {
      print("getHistory");
      var address = Storage.get(StorageKeys.addressETH) ?? "";
      var token = await profileCtrl.getTokenFirebase();

      Map<String, dynamic> body = {
        "_token": token,
        "address": address,
      };

      print(address);
      print(body);
      // เรียก API เพื่อดึงข้อมูลประวัติการทำธุรกรรม
      var response = await AppApi.post('${AppEnv.apiUrl}/getHistoryNew', body);

      print(response);
      if (response['statusCode'] == 200) {
        List<TransactionModel> parsed = [];

        for (var item in response['result']) {
          var model = TransactionModel.fromJson(item as Map<String, dynamic>);
          parsed.add(model);
        }

        all.assignAll(parsed); // ใช้ assignAll ดีกว่า clear + add
        sent.assignAll(parsed.where((e) => e.title?.toLowerCase() == 'sent'));
        receive.assignAll(parsed.where((e) => e.title?.toLowerCase() == 'recv' && e.to != 'Reward'));
        reward.assignAll(parsed.where((e) => e.title?.toLowerCase() == 'recv' && e.to == 'Reward'));
      } else {
        print("Error: ${response['message']}");
      }

      update();
    } catch (e) {
      print("Exception: $e");
    }
  }

}