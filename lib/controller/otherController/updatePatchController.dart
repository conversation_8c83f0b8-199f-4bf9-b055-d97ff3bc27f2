import 'dart:async';

import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';


class UpdatePatchController extends GetxController {

  RxDouble downloadProgressNotifier = 0.0.obs;
  RxBool restartApp = false.obs;

  //shorebird
  final shorebirdCodePush = ShorebirdCodePush();


  @override
  void onInit() {
    super.onInit();
    downloadUpdate();
  }

  // Future<void> checkPathUpdate() async {
  //   downloadUpdate();
  // }

  Future<void> downloadUpdate() async {
    Timer.periodic(const Duration(milliseconds: 50), (Timer t) async {
      bool isReadyToInstall = await shorebirdCodePush.isNewPatchReadyToInstall();
      if (downloadProgressNotifier.value < 100) {
        if (downloadProgressNotifier.value < 90) {
          downloadProgressNotifier.value += 1;

        } else if (isReadyToInstall) {
          downloadProgressNotifier.value += 1;
        }

        if(downloadProgressNotifier.value ==  90) {
          restartApp.value = true;
        }

        if(downloadProgressNotifier.value ==  100) {
          restartApp.value = true;
        }
        update();
      } else {
        t.cancel();
      }
    });
  }
}