import 'package:get/get.dart';
import 'package:likewallet/service/getStorage.dart';

class RewardCurrencyController extends GetxController {
  // Observable for selected reward currency
  RxString selectedRewardCurrency = 'like'.obs;
  
  // List of reward currencies
  final List<Map<String, String>> rewardCurrencies = const [
    {"key": "like", "label": "LIKE"},
    {"key": "btc", "label": "BTC - Bitcoin"},
    {"key": "gold", "label": "(ทอง) กรัม"},
  ];

  @override
  void onInit() {
    super.onInit();
    loadSavedRewardCurrency();
  }

  // Load saved reward currency from storage
  void loadSavedRewardCurrency() {
    final savedCurrency = Storage.get<String>('selected_reward_currency');
    if (savedCurrency != null && savedCurrency.isNotEmpty) {
      // Check if the saved currency exists in our reward currencies list
      final exists = rewardCurrencies.any((currency) => currency['key'] == savedCurrency);
      if (exists) {
        selectedRewardCurrency.value = savedCurrency;
      } else {
        // Default to LIKE if saved currency doesn't exist
        selectedRewardCurrency.value = 'like';
        saveRewardCurrency('like');
      }
    } else {
      // Default to LIKE if no saved currency
      selectedRewardCurrency.value = 'like';
      saveRewardCurrency('like');
    }
  }

  // Save reward currency to storage
  void saveRewardCurrency(String currencyKey) {
    selectedRewardCurrency.value = currencyKey;
    Storage.save('selected_reward_currency', currencyKey);
    update();
  }

  // Get current reward currency label
  String getCurrentRewardCurrencyLabel() {
    final currency = rewardCurrencies.firstWhere(
      (currency) => currency['key'] == selectedRewardCurrency.value,
      orElse: () => rewardCurrencies[0],
    );
    return currency['label'] ?? 'LIKE';
  }

  // Get reward currency by key
  Map<String, String>? getRewardCurrencyByKey(String key) {
    try {
      return rewardCurrencies.firstWhere((currency) => currency['key'] == key);
    } catch (e) {
      return null;
    }
  }

  // Check if a currency key is valid
  bool isValidRewardCurrency(String key) {
    return rewardCurrencies.any((currency) => currency['key'] == key);
  }
}
