#!/bin/bash

# Shorebird Testing Script
# This script helps you test the Shorebird update functionality

echo "🐦 Shorebird Testing Script"
echo "=========================="

# Set up environment
export PATH="$HOME/.shorebird/bin:$PATH"

# Check if we're in the right directory
if [ ! -f "shorebird.yaml" ]; then
    echo "❌ Error: shorebird.yaml not found. Please run this script from your Flutter project root."
    exit 1
fi

echo "✅ Found shorebird.yaml"

# Function to wait for user input
wait_for_input() {
    echo ""
    read -p "Press Enter to continue..." -r
}

# Function to check Shorebird status
check_status() {
    echo ""
    echo "📊 Checking Shorebird status..."
    shorebird doctor
}

# Function to create a release
create_release() {
    echo ""
    echo "📦 Creating Shorebird release..."
    echo "This will create a new release that can receive patches."
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        shorebird release android
    else
        echo "Release creation cancelled."
    fi
}

# Function to create a patch
create_patch() {
    echo ""
    echo "🚀 Creating Shorebird patch..."
    echo "Make sure you've made some code changes first!"
    read -p "Have you made code changes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        shorebird patch android
    else
        echo "Please make some code changes first, then run this again."
    fi
}

# Function to make a test change
make_test_change() {
    echo ""
    echo "✏️ Making a test change..."

    # Create a simple test change in the main screen
    if [ -f "lib/view/navigationBar/mainNavigator.dart" ]; then
        # Add a comment with timestamp to trigger a change
        timestamp=$(date +"%Y-%m-%d %H:%M:%S")
        echo "// Test change made at: $timestamp" >> lib/view/navigationBar/mainNavigator.dart
        echo "✅ Test change added to mainNavigator.dart"
        echo "   Added comment: // Test change made at: $timestamp"
    else
        echo "❌ Could not find mainNavigator.dart"
    fi
}

# Function to show current patch info
show_patch_info() {
    echo ""
    echo "📋 Current Shorebird Information:"
    echo "================================"

    if command -v shorebird &> /dev/null; then
        echo "Shorebird Version: $(shorebird --version | head -n 1)"

        if [ -f "shorebird.yaml" ]; then
            app_id=$(grep "app_id:" shorebird.yaml | cut -d' ' -f2)
            echo "App ID: $app_id"
        fi

        echo ""
        echo "📱 Testing Steps:"
        echo "Step A: Create a release first (use menu option 2)"
        echo "Step B: Install the release APK on your device"
        echo "Step C: Make code changes (use menu option 4)"
        echo "Step D: Create a patch (use menu option 3)"
        echo "Step E: Open your app and test the _checkForUpdates function"
        echo ""
        echo "🎯 In your app, you can:"
        echo "• Tap the blue 'Test Update Check' button"
        echo "• Tap the purple 'Test Page' button for detailed testing"
        echo "• Check console logs for debug output"
    else
        echo "❌ Shorebird CLI not found"
    fi
}

# Main menu function
show_menu() {
    echo ""
    echo "🐦 Shorebird Test Menu:"
    echo "1. Check Shorebird status"
    echo "2. Create release (do this first)"
    echo "3. Create patch (after making changes)"
    echo "4. Make test code change"
    echo "5. Show testing information & steps"
    echo "6. Exit"
    echo ""
}

# Main execution
main() {
    while true; do
        show_menu
        read -p "Choose an option (1-6): " choice

        case "$choice" in
            1)
                check_status
                wait_for_input
                ;;
            2)
                create_release
                wait_for_input
                ;;
            3)
                create_patch
                wait_for_input
                ;;
            4)
                make_test_change
                wait_for_input
                ;;
            5)
                show_patch_info
                wait_for_input
                ;;
            6)
                echo ""
                echo "👋 Goodbye!"
                exit 0
                ;;
            *)
                echo ""
                echo "❌ Invalid option '$choice'. Please choose 1-6."
                wait_for_input
                ;;
        esac
    done
}

# Start the script
main
