#!/bin/bash

# Shorebird Testing Script
# This script helps you test the Shorebird update functionality

echo "🐦 Shorebird Testing Script"
echo "=========================="

# Set up environment
export PATH="$HOME/.shorebird/bin:$PATH"

# Check if we're in the right directory
if [ ! -f "shorebird.yaml" ]; then
    echo "❌ Error: shorebird.yaml not found. Please run this script from your Flutter project root."
    exit 1
fi

echo "✅ Found shorebird.yaml"

# Function to check Shorebird status
check_status() {
    echo ""
    echo "📊 Checking Shorebird status..."
    shorebird doctor
}

# Function to create a release
create_release() {
    echo ""
    echo "📦 Creating Shorebird release..."
    echo "This will create a new release that can receive patches."
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        shorebird release android
    else
        echo "Release creation cancelled."
    fi
}

# Function to create a patch
create_patch() {
    echo ""
    echo "🚀 Creating Shorebird patch..."
    echo "Make sure you've made some code changes first!"
    read -p "Have you made code changes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        shorebird patch android
    else
        echo "Please make some code changes first, then run this again."
    fi
}

# Function to make a test change
make_test_change() {
    echo ""
    echo "✏️ Making a test change..."
    
    # Create a simple test change in the main screen
    if [ -f "lib/view/navigationBar/mainNavigator.dart" ]; then
        # Add a comment with timestamp to trigger a change
        timestamp=$(date +"%Y-%m-%d %H:%M:%S")
        echo "// Test change made at: $timestamp" >> lib/view/navigationBar/mainNavigator.dart
        echo "✅ Test change added to mainNavigator.dart"
        echo "   Added comment: // Test change made at: $timestamp"
    else
        echo "❌ Could not find mainNavigator.dart"
    fi
}

# Function to show current patch info
show_patch_info() {
    echo ""
    echo "📋 Current Shorebird Information:"
    echo "================================"
    
    if command -v shorebird &> /dev/null; then
        echo "Shorebird Version: $(shorebird --version | head -n 1)"
        
        if [ -f "shorebird.yaml" ]; then
            app_id=$(grep "app_id:" shorebird.yaml | cut -d' ' -f2)
            echo "App ID: $app_id"
        fi
        
        echo ""
        echo "To test the update function:"
        echo "1. Create a release first (option 2)"
        echo "2. Install the release APK on your device"
        echo "3. Make code changes (option 4)"
        echo "4. Create a patch (option 3)"
        echo "5. Test the _checkForUpdates function in your app"
    else
        echo "❌ Shorebird CLI not found"
    fi
}

# Main menu
while true; do
    echo ""
    echo "🐦 Shorebird Test Menu:"
    echo "1. Check Shorebird status"
    echo "2. Create release (do this first)"
    echo "3. Create patch (after making changes)"
    echo "4. Make test code change"
    echo "5. Show patch information"
    echo "6. Exit"
    echo ""
    read -p "Choose an option (1-6): " choice

    case $choice in
        1)
            check_status
            ;;
        2)
            create_release
            ;;
        3)
            create_patch
            ;;
        4)
            make_test_change
            ;;
        5)
            show_patch_info
            ;;
        6)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid option. Please choose 1-6."
            ;;
    esac
done
